{"version": 3, "file": "solidity-files-cache.js", "sourceRoot": "", "sources": ["../../src/builtin-tasks/utils/solidity-files-cache.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,kDAA0B;AAC1B,wDAA+B;AAC/B,yCAA2B;AAC3B,2CAA6B;AAE7B,wDAAyE;AAEzE,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kCAAkC,CAAC,CAAC;AAEtD,MAAM,cAAc,GAAG,gBAAgB,CAAC;AAExC,MAAM,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC;IAC7B,oBAAoB,EAAE,CAAC,CAAC,MAAM;IAC9B,WAAW,EAAE,CAAC,CAAC,MAAM;IACrB,UAAU,EAAE,CAAC,CAAC,MAAM;IACpB,UAAU,EAAE,CAAC,CAAC,GAAG;IACjB,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;IAC1B,cAAc,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;IACjC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;CAC7B,CAAC,CAAC;AAEH,MAAM,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC;IACxB,OAAO,EAAE,CAAC,CAAC,MAAM;IACjB,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,eAAe,CAAC;CAC3C,CAAC,CAAC;AAiBH,MAAa,kBAAkB;IACtB,MAAM,CAAC,WAAW;QACvB,OAAO,IAAI,kBAAkB,CAAC;YAC5B,OAAO,EAAE,cAAc;YACvB,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,YAAY,CAC9B,sBAA8B;QAE9B,IAAI,QAAQ,GAAU;YACpB,OAAO,EAAE,cAAc;YACvB,KAAK,EAAE,EAAE;SACV,CAAC;QACF,IAAI,MAAM,kBAAO,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,QAAQ,GAAG,MAAM,kBAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;SAC3D;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE3C,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;YACpB,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;YAClD,OAAO,kBAAkB,CAAC;SAC3B;QAED,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAE7C,OAAO,IAAI,kBAAkB,CAAC;YAC5B,OAAO,EAAE,cAAc;YACvB,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;IACL,CAAC;IAED,YAAoB,MAAa;QAAb,WAAM,GAAN,MAAM,CAAO;IAAG,CAAC;IAE9B,KAAK,CAAC,sBAAsB;QACjC,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE;YACxD,IAAI,CAAC,CAAC,MAAM,kBAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,EAAE;gBAC7C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;aAChC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,sBAA8B;QACrD,MAAM,kBAAO,CAAC,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,EAAE;YAC5D,MAAM,EAAE,CAAC;SACV,CAAC,CAAC;IACL,CAAC;IAEM,OAAO,CAAC,YAAoB,EAAE,KAAiB;QACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC;IAC1C,CAAC;IAEM,UAAU;QACf,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAEM,QAAQ,CAAC,IAAY;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAEM,WAAW,CAAC,IAAY;QAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAEM,cAAc,CACnB,YAAoB,EACpB,WAAmB,EACnB,UAAuB;QAEvB,MAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAA4B,CAAC;QAErE,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAE/C,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,kDAAkD;YAClD,OAAO,IAAI,CAAC;SACb;QAED,IAAI,UAAU,CAAC,WAAW,KAAK,WAAW,EAAE;YAC1C,OAAO,IAAI,CAAC;SACb;QAED,IACE,UAAU,KAAK,SAAS;YACxB,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,EAC3C;YACA,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAhGD,gDAgGC;AAED,SAAgB,yBAAyB,CAAC,KAAyB;IACjE,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,yCAA6B,CAAC,CAAC;AAC/D,CAAC;AAFD,8DAEC"}