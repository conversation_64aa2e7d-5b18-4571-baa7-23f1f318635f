{"version": 3, "file": "test.js", "sourceRoot": "", "sources": ["../src/builtin-tasks/test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,4DAAoC;AACpC,gDAAwB;AAExB,qDAA6D;AAC7D,mEAAmE;AACnE,oDAAuD;AACvD,8DAAsD;AACtD,4EAI6C;AAC7C,sFAA4F;AAC5F,wHAAkI;AAClI,sDAAqD;AACrD,wDAAgE;AAChE,8DAAqE;AAErE,6CAOsB;AAEtB,IAAA,oBAAO,EAAC,qCAAwB,CAAC;KAC9B,kCAAkC,CACjC,WAAW,EACX,mCAAmC,EACnC,EAAE,CACH;KACA,SAAS,CAAC,KAAK,EAAE,EAAE,SAAS,EAA2B,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;IACtE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,MAAM,sBAAsB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACjD,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAC/B,CAAC;QAEF,OAAO,sBAAsB,CAAC;KAC/B;IAED,MAAM,OAAO,GAAG,MAAM,IAAA,8BAAmB,EACvC,MAAM,CAAC,KAAK,CAAC,KAAK,EAClB,qCAAgB,CACjB,CAAC;IAEF,IAAI,CAAC,IAAA,4CAAuB,EAAC,MAAM,CAAC,EAAE;QACpC,OAAO,OAAO,CAAC;KAChB;IAED,MAAM,OAAO,GAAG,MAAM,IAAA,8BAAmB,EACvC,MAAM,CAAC,KAAK,CAAC,KAAK,EAClB,qCAAgB,CACjB,CAAC;IAEF,OAAO,CAAC,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC;AAClC,CAAC,CAAC,CAAC;AAEL,IAAA,oBAAO,EAAC,6CAAgC,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC,CAAC,CAAC;AAE1D,IAAI,eAAe,GAAG,KAAK,CAAC;AAC5B,IAAA,oBAAO,EAAC,sCAAyB,CAAC;KAC/B,OAAO,CAAC,UAAU,EAAE,uBAAuB,CAAC;KAC5C,OAAO,CAAC,MAAM,EAAE,iDAAiD,CAAC;KAClE,gBAAgB,CACf,MAAM,EACN,oDAAoD,CACrD;KACA,kCAAkC,CACjC,WAAW,EACX,mCAAmC,EACnC,EAAE,CACH;KACA,SAAS,CACR,KAAK,EACH,QAKC,EACD,EAAE,MAAM,EAAE,EACV,EAAE;IACF,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,wDAAa,OAAO,GAAC,CAAC;IAEjD,MAAM,WAAW,GAAiB,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAEtD,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;QAC/B,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;KAClC;IACD,IAAI,QAAQ,CAAC,IAAI,EAAE;QACjB,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,QAAQ,EAAE;QACrB,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;KAC7B;IAED,IAAI,WAAW,CAAC,QAAQ,KAAK,IAAI,EAAE;QACjC,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC;QAC/C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC9C,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACvC;QACD,WAAW,CAAC,OAAO,GAAG,YAAY,CAAC;KACpC;IAED,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;IACrC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAE1D,uEAAuE;IACvE,uDAAuD;IACvD,MAAM,kBAAkB,GAAG,MAAM,IAAA,mCAAqB,GAAE,CAAC;IACzD,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,KAAK,QAAQ,CAAC;IAC1D,MAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAClD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CACtB,CAAC;IACF,IAAI,YAAY,IAAI,UAAU,EAAE;QAC9B,sEAAsE;QACtE,wEAAwE;QACxE,eAAe;QACf,IAAI,eAAe,EAAE;YACnB,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,aAAa,CAAC,6BAA6B,CACnD,CAAC;SACH;QACD,eAAe,GAAG,IAAI,CAAC;QAEvB,2EAA2E;QAC3E,kCAAkC;QAClC,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;KAC9B;IAED,MAAM,YAAY,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,EAAE;QACzD,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,OAAO,EAAE,CAAC;IAEhB,OAAO,YAAY,CAAC;AACtB,CAAC,CACF,CAAC;AAEJ,IAAA,oBAAO,EAAC,oDAAuC,CAAC,CAAC,SAAS,CACxD,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE;IAC/B,IAAI,OAAO,CAAC,IAAI,KAAK,gCAAoB,EAAE;QACzC,OAAO;KACR;IAED,MAAM,SAAS,GAAG,IAAA,gCAAmB,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,IAAA,sEAAwC,EAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC5E,CAAC,CACF,CAAC;AAEF,IAAA,iBAAI,EAAC,sBAAS,EAAE,kBAAkB,CAAC;KAChC,kCAAkC,CACjC,WAAW,EACX,mCAAmC,EACnC,EAAE,CACH;KACA,OAAO,CAAC,WAAW,EAAE,wCAAwC,CAAC;KAC9D,OAAO,CAAC,UAAU,EAAE,uBAAuB,CAAC;KAC5C,OAAO,CAAC,MAAM,EAAE,iDAAiD,CAAC;KAClE,gBAAgB,CACf,MAAM,EACN,oDAAoD,CACrD;KACA,SAAS,CACR,KAAK,EACH,EACE,SAAS,EACT,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,IAAI,GAOL,EACD,EAAE,GAAG,EAAE,OAAO,EAAE,EAChB,EAAE;IACF,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,GAAG,CAAC,yBAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KAC1C;IAED,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,qCAAwB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IAEjE,MAAM,GAAG,CAAC,6CAAgC,CAAC,CAAC;IAE5C,MAAM,GAAG,CAAC,oDAAuC,CAAC,CAAC;IAEnD,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,sCAAyB,EAAE;QACxD,SAAS,EAAE,KAAK;QAChB,QAAQ;QACR,IAAI;QACJ,IAAI;KACL,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,IAAI,KAAK,gCAAoB,EAAE;QACzC,MAAM,mBAAmB,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,CACrD,oCAAoC,CACrC,CAAC;QAEF,IAAI,mBAAmB,KAAK,CAAC,EAAE;YAC7B,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,sBAAsB,mBAAmB,IAAI,IAAA,mBAAS,EACpD,mBAAmB,EACnB,aAAa,CACd,6CAA6C,CAC/C,CACF,CAAC;SACH;KACF;IAED,OAAO,CAAC,QAAQ,GAAG,YAAY,CAAC;IAChC,OAAO,YAAY,CAAC;AACtB,CAAC,CACF,CAAC"}