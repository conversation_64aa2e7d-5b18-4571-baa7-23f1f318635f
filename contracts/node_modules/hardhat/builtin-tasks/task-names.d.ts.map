{"version": 3, "file": "task-names.d.ts", "sourceRoot": "", "sources": ["../src/builtin-tasks/task-names.ts"], "names": [], "mappings": "AAAA,eAAO,MAAM,UAAU,UAAU,CAAC;AAElC,eAAO,MAAM,UAAU,UAAU,CAAC;AAClC,eAAO,MAAM,iBAAiB,iBAAiB,CAAC;AAEhD,eAAO,MAAM,YAAY,YAAY,CAAC;AACtC,eAAO,MAAM,kCAAkC,kCACd,CAAC;AAClC,eAAO,MAAM,qBAAqB,qBAAqB,CAAC;AACxD,eAAO,MAAM,sCAAsC,sCACd,CAAC;AACtC,eAAO,MAAM,sCAAsC,sCACd,CAAC;AACtC,eAAO,MAAM,+BAA+B,+BAA+B,CAAC;AAC5E,eAAO,MAAM,kCAAkC,2CACL,CAAC;AAC3C,eAAO,MAAM,2BAA2B,oCAAoC,CAAC;AAC7E,eAAO,MAAM,0CAA0C,0CACd,CAAC;AAC1C,eAAO,MAAM,0CAA0C,0CACd,CAAC;AAC1C,eAAO,MAAM,kDAAkD,kDACd,CAAC;AAClD,eAAO,MAAM,6CAA6C,6CACd,CAAC;AAC7C,eAAO,MAAM,4CAA4C,4CACd,CAAC;AAC5C,eAAO,MAAM,4CAA4C,4CACd,CAAC;AAC5C,eAAO,MAAM,iCAAiC,iCAAiC,CAAC;AAChF,eAAO,MAAM,4CAA4C,4CACd,CAAC;AAC5C,eAAO,MAAM,0CAA0C,0CACd,CAAC;AAC1C,eAAO,MAAM,kCAAkC,kCACd,CAAC;AAClC,eAAO,MAAM,wCAAwC,wCACd,CAAC;AACxC,eAAO,MAAM,6BAA6B,6BAA6B,CAAC;AACxE,eAAO,MAAM,kCAAkC,kCACd,CAAC;AAClC,eAAO,MAAM,oCAAoC,oCACd,CAAC;AACpC,eAAO,MAAM,iDAAiD,iDACd,CAAC;AACjD,eAAO,MAAM,+CAA+C,+CACd,CAAC;AAC/C,eAAO,MAAM,gCAAgC,gCAAgC,CAAC;AAC9E,eAAO,MAAM,8BAA8B,8BAA8B,CAAC;AAC1E,eAAO,MAAM,kCAAkC,kCACd,CAAC;AAClC,eAAO,MAAM,4CAA4C,4CACd,CAAC;AAC5C,eAAO,MAAM,oCAAoC,oCACd,CAAC;AACpC,eAAO,MAAM,0DAA0D,0DACd,CAAC;AAC1D,eAAO,MAAM,sDAAsD,sDACd,CAAC;AACtD,eAAO,MAAM,0DAA0D,0DACd,CAAC;AAC1D,eAAO,MAAM,4CAA4C,4CACd,CAAC;AAC5C,eAAO,MAAM,sCAAsC,sCACd,CAAC;AAEtC,eAAO,MAAM,YAAY,YAAY,CAAC;AAEtC,eAAO,MAAM,YAAY,YAAY,CAAC;AACtC,eAAO,MAAM,iCAAiC,kCACb,CAAC;AAClC,eAAO,MAAM,8CAA8C,+CACb,CAAC;AAC/C,eAAO,MAAM,iCAAiC,iCAAiC,CAAC;AAEhF,eAAO,MAAM,SAAS,SAAS,CAAC;AAEhC,eAAO,MAAM,QAAQ,QAAQ,CAAC;AAE9B,eAAO,MAAM,SAAS,SAAS,CAAC;AAChC,eAAO,MAAM,sBAAsB,sBAAsB,CAAC;AAC1D,eAAO,MAAM,uBAAuB,uBAAuB,CAAC;AAC5D,eAAO,MAAM,wBAAwB,wBAAwB,CAAC;AAC9D,eAAO,MAAM,sBAAsB,sBAAsB,CAAC;AAE1D,eAAO,MAAM,SAAS,SAAS,CAAC;AAEhC,eAAO,MAAM,uCAAuC,mCAClB,CAAC;AACnC,eAAO,MAAM,yBAAyB,yBAAyB,CAAC;AAChE,eAAO,MAAM,wBAAwB,wBAAwB,CAAC;AAC9D,eAAO,MAAM,gCAAgC,gCAAgC,CAAC"}