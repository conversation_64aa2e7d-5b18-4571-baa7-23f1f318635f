export declare const HARDHAT_NAME = "Hardhat";
export declare const HARDHAT_EXECUTABLE_NAME = "hardhat";
export declare const HARDHAT_NETWORK_NAME = "hardhat";
export declare const SOLIDITY_FILES_CACHE_FILENAME = "solidity-files-cache.json";
export declare const HARDHAT_NETWORK_SUPPORTED_HARDFORKS: string[];
export declare const HARDHAT_MEMPOOL_SUPPORTED_ORDERS: readonly ["fifo", "priority"];
export declare const ARTIFACT_FORMAT_VERSION = "hh-sol-artifact-1";
export declare const DEBUG_FILE_FORMAT_VERSION = "hh-sol-dbg-1";
export declare const BUILD_INFO_FORMAT_VERSION = "hh-sol-build-info-1";
export declare const BUILD_INFO_DIR_NAME = "build-info";
export declare const EDIT_DISTANCE_THRESHOLD = 3;
export declare const HARDHAT_NETWORK_RESET_EVENT = "hardhatNetworkReset";
export declare const HARDHAT_NETWORK_REVERT_SNAPSHOT_EVENT = "hardhatNetworkRevertSnapshot";
//# sourceMappingURL=constants.d.ts.map