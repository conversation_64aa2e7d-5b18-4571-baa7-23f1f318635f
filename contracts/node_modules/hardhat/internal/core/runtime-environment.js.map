{"version": 3, "file": "runtime-environment.js", "sourceRoot": "", "sources": ["../../src/internal/core/runtime-environment.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAoB1B,4CAAyC;AAEzC,qDAAwD;AACxD,4DAAqE;AACrE,qCAAwC;AACxC,+CAAuC;AACvC,2DAA0D;AAC1D,yEAAoF;AACpF,+DAAoE;AACpE,qDAK0B;AAC1B,uCAAmD;AAEnD,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEtC,MAAa,WAAW;IAkBtB;;;;;;;;;;;;;OAaG;IACH,YACkB,MAAqB,EACrB,gBAAkC,EAClC,KAAe,EACf,MAAiB,EACjC,uBAA8C,EAAE,EAChC,aAAgC,EAAE,EAClD,oBAAwC,EAAE;QAN1B,WAAM,GAAN,MAAM,CAAe;QACrB,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,UAAK,GAAL,KAAK,CAAU;QACf,WAAM,GAAN,MAAM,CAAW;QAEjB,eAAU,GAAV,UAAU,CAAwB;QAtB7C,YAAO,GAAW,IAAA,+BAAiB,GAAE,CAAC;QA+D7C;;;;;;;;;WASG;QACa,QAAG,GAAoB,KAAK,EAC1C,cAAc,EACd,aAAa,GAAG,EAAE,EAClB,gBAAgB,GAAG,EAAE,EACrB,iBAA+B,EAC/B,EAAE;YACF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAA,0BAAmB,EAAC,cAAc,CAAC,CAAC;YAE5D,IAAI,cAAc,CAAC;YACnB,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAClC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;aAC9B;iBAAM;gBACL,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC3C,IAAI,eAAe,KAAK,SAAS,EAAE;oBACjC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE;wBAC1D,KAAK;qBACN,CAAC,CAAC;iBACJ;gBAED,cAAc,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;gBAC/C,GAAG,CAAC,2BAA2B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC/C;YAED,IAAI,cAAc,KAAK,SAAS,EAAE;gBAChC,IAAI,KAAK,KAAK,SAAS,EAAE;oBACvB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE;wBAChE,KAAK;wBACL,IAAI;qBACL,CAAC,CAAC;iBACJ;gBAED,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE;oBACzD,IAAI;iBACL,CAAC,CAAC;aACJ;YAED,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAC3D,cAAc,EACd,aAAa,EACb,gBAAgB,CACjB,CAAC;YAEF,IAAI,WAAoC,CAAC;YACzC,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK,IAAI,EAAE;gBAC7C,WAAW,GAAG,IAAA,kCAAiB,EAAC,IAAI,CAAC,CAAC;gBAEtC,IAAI,iBAAiB,KAAK,SAAS,EAAE;oBACnC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC9C;qBAAM;oBACL,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;iBACrC;aACF;YAED,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAClC,cAAc,EACd,qBAAqB,EACrB,gBAAgB,EAChB,WAAW,CACZ,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;gBACV,IAAA,2CAA0B,EAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAE5D,sFAAsF;gBACtF,MAAM,CAAC,CAAC;aACT;oBAAS;gBACR,IAAI,WAAW,KAAK,SAAS,EAAE;oBAC7B,IAAA,oCAAmB,EAAC,WAAW,CAAC,CAAC;iBAClC;aACF;QACH,CAAC,CAAC;QAvHA,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAE1C,MAAM,WAAW,GACf,gBAAgB,CAAC,OAAO,KAAK,SAAS;YACpC,CAAC,CAAC,gBAAgB,CAAC,OAAO;YAC1B,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;QAE5B,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAEnD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE;gBACtD,OAAO,EAAE,WAAW;aACrB,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEvD,MAAM,QAAQ,GAAG,IAAI,uDAAiC,CAAC,KAAK,IAAI,EAAE;YAChE,GAAG,CAAC,iCAAiC,WAAW,EAAE,CAAC,CAAC;YACpD,OAAO,IAAA,6BAAc,EACnB,MAAM,EACN,WAAW,EACX,IAAI,CAAC,SAAS,EACd,iBAAiB,CAClB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG;YACb,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,aAAa;YACrB,QAAQ;SACT,CAAC;QAEF,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAElD,oBAAoB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D,CAAC;IAqFD;;;;;;OAMG;IACI,cAAc,CACnB,YAAsB,WAAW,CAAC,uBAAuB;QAEzD,MAAM,WAAW,GAAG,MAAa,CAAC;QAElC,MAAM,cAAc,GAA4B,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC;QAEpC,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC;QAEvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC/C,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC3B,SAAS;aACV;YAED,cAAc,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;YACvC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAC1B;QAED,OAAO,GAAG,EAAE;YACV,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC3C,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC3B,SAAS;iBACV;gBAED,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC;gBAC9B,WAAW,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;aACxC;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,kBAAkB,CAC9B,cAA8B,EAC9B,aAA4B,EAC5B,gBAAkC,EAClC,WAAyB;QAEzB,IAAI,gBAAqB,CAAC;QAE1B,IAAI,cAAc,YAAY,2CAAwB,EAAE;YACtD,gBAAgB,GAAG,KAAK,EACtB,iBAAgC,aAAa,EAC7C,oBAAsC,gBAAgB,EACtD,EAAE;gBACF,GAAG,CAAC,oBAAoB,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;gBAE/C,IAAI,WAAW,KAAK,SAAS,EAAE;oBAC7B,OAAO,IAAI,CAAC,kBAAkB,CAC5B,cAAc,CAAC,oBAAoB,EACnC,cAAc,EACd,iBAAiB,CAClB,CAAC;iBACH;gBAED,MAAM,iBAAiB,GAAG,IAAA,wCAAuB,EAAC,WAAW,CAAC,CAAC;gBAC/D,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAE7C,IAAI;oBACF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAClC,cAAc,CAAC,oBAAoB,EACnC,cAAc,EACd,iBAAiB,EACjB,iBAAiB,CAClB,CAAC;iBACH;wBAAS;oBACR,IAAA,oCAAmB,EAAC,iBAAiB,CAAC,CAAC;iBACxC;YACH,CAAC,CAAC;YAEF,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC;SACnC;aAAM;YACL,gBAAgB,GAAG,KAAK,IAAI,EAAE;gBAC5B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;oBACrE,QAAQ,EAAE,cAAc,CAAC,IAAI;iBAC9B,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,gBAAgB,CAAC,SAAS,GAAG,KAAK,CAAC;SACpC;QAED,MAAM,QAAQ,GAAoC,gBAAgB,CAAC;QAEnE,MAAM,WAAW,GAAG,MAAa,CAAC;QAClC,MAAM,gBAAgB,GAAQ,WAAW,CAAC,QAAQ,CAAC;QACnD,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEhC,yEAAyE;QACzE,uEAAuE;QACvE,4EAA4E;QAC5E,MAAM,UAAU,GAAG,IAAI,KAAK,CAAc,IAAI,EAAE;YAC9C,GAAG,CAAC,MAAmB,EAAE,CAAkB,EAAE,QAAa;gBACxD,IAAI,CAAC,KAAK,KAAK,EAAE;oBACf,OAAO,CACL,KAAa,EACb,cAA6B,EAC7B,iBAAmC,EACnC,EAAE,CACD,MAAc,CAAC,GAAG,CACjB,KAAK,EACL,cAAc,EACd,EAAE,GAAG,iBAAiB,EAAE,GAAG,gBAAgB,EAAE,EAAE,sCAAsC;oBACrF,WAAW,CACZ,CAAC;iBACL;gBAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;YAC1C,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK,IAAI,EAAE;YAC7C,4DAA4D;YAC3D,UAAkB,CAAC,YAAY,GAAG,KAAK,EACtC,KAAa,EACb,CAAqB,EACrB,EAAE;gBACF,MAAM,YAAY,GAAG,IAAA,kCAAiB,EAAC,KAAK,CAAC,CAAC;gBAC9C,WAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACzC,IAAI;oBACF,OAAO,MAAM,CAAC,EAAE,CAAC;iBAClB;wBAAS;oBACR,IAAA,oCAAmB,EAAC,YAAY,CAAC,CAAC;iBACnC;YACH,CAAC,CAAC;YAED,UAAkB,CAAC,gBAAgB,GAAG,CAAC,KAAa,EAAE,CAAY,EAAE,EAAE;gBACrE,MAAM,YAAY,GAAG,IAAA,kCAAiB,EAAC,KAAK,CAAC,CAAC;gBAC9C,WAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACzC,IAAI;oBACF,OAAO,CAAC,EAAE,CAAC;iBACZ;wBAAS;oBACR,IAAA,oCAAmB,EAAC,YAAY,CAAC,CAAC;iBACnC;YACH,CAAC,CAAC;SACH;QAED,MAAM,kBAAkB,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;QAEvD,IAAI;YACF,OAAO,MAAM,cAAc,CAAC,MAAM,CAAC,aAAa,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SACzE;gBAAS;YACR,kBAAkB,EAAE,CAAC;YACrB,WAAW,CAAC,QAAQ,GAAG,gBAAgB,CAAC;SACzC;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,0BAA0B,CAChC,cAA8B,EAC9B,aAA4B,EAC5B,gBAAkC;QAElC,MAAM,EACJ,IAAI,EAAE,QAAQ,EACd,gBAAgB,EAChB,0BAA0B,GAC3B,GAAG,cAAc,CAAC;QAEnB,MAAM,6BAA6B,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAEtE,oCAAoC;QACpC,MAAM,uBAAuB,GAAG;YAC9B,GAAG,6BAA6B;YAChC,GAAG,0BAA0B;SAC9B,CAAC;QAEF,MAAM,iBAAiB,GAAkB,EAAE,CAAC;QAE5C,KAAK,MAAM,eAAe,IAAI,uBAAuB,EAAE;YACrD,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC;YACvC,MAAM,aAAa,GACjB,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,CAAC;YAEtE,MAAM,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CACjD,eAAe,EACf,aAAa,EACb,cAAc,CAAC,IAAI,CACpB,CAAC;YAEF,IAAI,qBAAqB,KAAK,SAAS,EAAE;gBACvC,iBAAiB,CAAC,SAAS,CAAC,GAAG,qBAAqB,CAAC;aACtD;SACF;QAED,2DAA2D;QAC3D,OAAO,EAAE,GAAG,aAAa,EAAE,GAAG,iBAAiB,EAAE,CAAC;IACpD,CAAC;IAED;;;;;;OAMG;IACK,gBAAgB,CACtB,eAAqC,EACrC,aAAkB,EAClB,QAAgB;QAEhB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,eAAe,CAAC;QAE3D,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,IAAI,UAAU,EAAE;gBACd,uDAAuD;gBACvD,OAAO,YAAY,CAAC;aACrB;YAED,0CAA0C;YAC1C,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE;gBAC7D,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;SACJ;QAED,kDAAkD;QAClD,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;QAE1D,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;;;;;;OAOG;IACK,oBAAoB,CAC1B,eAAqC,EACrC,aAAkB;QAElB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC;QAE9D,oGAAoG;QACpG,yDAAyD;QACzD,MAAM,sBAAsB,GAAG,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QAE5E,KAAK,MAAM,KAAK,IAAI,sBAAsB,EAAE;YAC1C,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SACjC;IACH,CAAC;;AAzauB,mCAAuB,GAAa;IAC1D,gBAAgB;IAChB,kBAAkB;IAClB,oBAAoB;IACpB,YAAY;CACb,AAL8C,CAK7C;AANS,kCAAW"}