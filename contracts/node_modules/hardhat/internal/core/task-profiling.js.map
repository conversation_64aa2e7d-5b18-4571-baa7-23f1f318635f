{"version": 3, "file": "task-profiling.js", "sourceRoot": "", "sources": ["../../src/internal/core/task-profiling.ts"], "names": [], "mappings": ";;;AAQA,SAAgB,iBAAiB,CAAC,IAAY;IAC5C,OAAO;QACL,IAAI;QACJ,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;QAC9B,QAAQ,EAAE,EAAE;KACb,CAAC;AACJ,CAAC;AAND,8CAMC;AAED,SAAgB,mBAAmB,CAAC,WAAwB;IAC1D,WAAW,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;AAC5C,CAAC;AAFD,kDAEC;AAED,SAAgB,uBAAuB,CAAC,WAAwB;IAC9D,OAAO,iBAAiB,CAAC,UAAU,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;AACzD,CAAC;AAFD,0DAEC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,OAAoB,EACpB,gBAAgB,GAAG,KAAK;IAExB,IAAI,gBAAgB,EAAE;QACpB,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;QACxB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;SACvB;KACF;SAAM;QACL,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE;YACnD,IAAI,CAAC,KAAK,CAAC,EAAE;gBACX,SAAS;aACV;YACD,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,IAAI,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,GAAI,EAAE;gBAChC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC1B,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IAED,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;KAC7C;AACH,CAAC;AAzBD,oDAyBC"}