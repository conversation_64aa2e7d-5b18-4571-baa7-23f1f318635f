{"version": 3, "file": "task-definitions.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/core/tasks/task-definitions.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,UAAU,EACV,YAAY,EAEZ,eAAe,EACf,mBAAmB,EACnB,eAAe,EACf,aAAa,EACb,cAAc,EACd,cAAc,EACd,QAAQ,EACT,MAAM,gBAAgB,CAAC;AAYxB;;;;;;;GAOG;AACH,qBAAa,oBAAqB,YAAW,cAAc;aA+BvC,SAAS,EAAE,OAAO;IA9BpC,IAAW,IAAI,WAEd;IACD,IAAW,KAAK,uBAEf;IACD,IAAW,WAAW,uBAErB;IACD,SAAgB,gBAAgB,EAAE,mBAAmB,CAAM;IAC3D,SAAgB,0BAA0B,EAAE,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAM;IACtE,MAAM,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;IAEzC,OAAO,CAAC,qBAAqB,CAAc;IAC3C,OAAO,CAAC,iBAAiB,CAAU;IACnC,OAAO,CAAC,2BAA2B,CAAU;IAC7C,OAAO,CAAC,MAAM,CAAC,CAAS;IACxB,OAAO,CAAC,KAAK,CAAS;IACtB,OAAO,CAAC,YAAY,CAAC,CAAS;IAE9B;;;;;;;OAOG;gBAED,cAAc,EAAE,cAAc,EACd,SAAS,GAAE,OAAe;IAe5C;;;OAGG;IACI,cAAc,CAAC,WAAW,EAAE,MAAM;IAKzC;;;OAGG;IACI,SAAS,CAAC,cAAc,SAAS,aAAa,EACnD,MAAM,EAAE,UAAU,CAAC,cAAc,CAAC;IAOpC;;;;;;;;;;;OAWG;IACI,QAAQ,CAAC,CAAC,EACf,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAChB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EACtB,UAAU,GAAE,OAAoC,GAC/C,IAAI;IAqDP;;;;;;;;;OASG;IACI,gBAAgB,CAAC,CAAC,EACvB,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAChB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GACrB,IAAI;IAIP;;;;;;;;;OASG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,MAAM;IAiBjD;;;;;;;;;;;;;;OAcG;IACI,kBAAkB,CAAC,CAAC,EACzB,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAChB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EACtB,UAAU,UAA6B,GACtC,IAAI;IAyDP;;;;;;;;;OASG;IACI,0BAA0B,CAAC,CAAC,EACjC,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAChB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GACrB,IAAI;IAIP;;;;;;;;;OASG;IACI,0BAA0B,CAAC,CAAC,EACjC,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EACtB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EACtB,UAAU,UAA6B,GACtC,IAAI;IA6DP;;;;;;;;;;OAUG;IACI,kCAAkC,CAAC,CAAC,EACzC,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EACtB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GACrB,IAAI;IAUP;;;;OAIG;IACH,OAAO,CAAC,6BAA6B;IAarC;;;;OAIG;IACH,OAAO,CAAC,8BAA8B;IAStC;;;;;;OAMG;IACH,OAAO,CAAC,oBAAoB;IAmB5B;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAOxB;;;;;;;OAOG;IACH,OAAO,CAAC,0CAA0C;IAelD,OAAO,CAAC,wBAAwB;IAchC,OAAO,CAAC,wCAAwC;IAgBhD,OAAO,CAAC,cAAc;IAItB,OAAO,CAAC,yCAAyC;CAelD;AAED;;;;;;;;GAQG;AACH,qBAAa,wBAAyB,YAAW,cAAc;aAK3C,oBAAoB,EAAE,cAAc;aACpC,SAAS,EAAE,OAAO;IALpC,OAAO,CAAC,YAAY,CAAC,CAAS;IAC9B,OAAO,CAAC,OAAO,CAAC,CAA4B;gBAG1B,oBAAoB,EAAE,cAAc,EACpC,SAAS,GAAE,OAAe;IAM5C;;;OAGG;IACI,cAAc,CAAC,WAAW,EAAE,MAAM;IAKzC;;;OAGG;IACI,SAAS,CAAC,cAAc,SAAS,aAAa,EACnD,MAAM,EAAE,UAAU,CAAC,cAAc,CAAC;IAOpC;;OAEG;IACH,IAAW,KAAK,uBAEf;IAED;;OAEG;IACH,IAAW,IAAI,WAEd;IAED;;;OAGG;IACH,IAAW,WAAW,uBAMrB;IAED;;;OAGG;IACH,IAAW,MAAM,oBAMhB;IAED;;OAEG;IACH,IAAW,gBAAgB,wBAE1B;IAED;;OAEG;IACH,IAAW,0BAA0B,2BAEpC;IAED;;OAEG;IACI,QAAQ,CAAC,CAAC,EACf,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAChB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EACtB,UAAU,CAAC,EAAE,OAAO,GACnB,IAAI;IASP;;OAEG;IACI,gBAAgB,CAAC,CAAC,EACvB,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,YAAY,CAAC,EAAE,CAAC,EAChB,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GACrB,IAAI;IAUP;;OAEG;IACI,kBAAkB,CAAC,CAAC,EACzB,KAAK,EAAE,MAAM,EACb,YAAY,CAAC,EAAE,MAAM,EACrB,aAAa,CAAC,EAAE,CAAC,EACjB,KAAK,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EACvB,WAAW,CAAC,EAAE,OAAO,GACpB,IAAI;IAMP;;OAEG;IACI,0BAA0B,CAAC,CAAC,EACjC,KAAK,EAAE,MAAM,EACb,YAAY,CAAC,EAAE,MAAM,EACrB,aAAa,CAAC,EAAE,CAAC,EACjB,KAAK,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GACtB,IAAI;IAMP;;OAEG;IACI,0BAA0B,CAAC,CAAC,EACjC,KAAK,EAAE,MAAM,EACb,YAAY,CAAC,EAAE,MAAM,EACrB,aAAa,CAAC,EAAE,CAAC,EAAE,EACnB,KAAK,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EACvB,WAAW,CAAC,EAAE,OAAO,GACpB,IAAI;IAMP;;OAEG;IACI,kCAAkC,CAAC,CAAC,EACzC,KAAK,EAAE,MAAM,EACb,YAAY,CAAC,EAAE,MAAM,EACrB,aAAa,CAAC,EAAE,CAAC,EAAE,EACnB,KAAK,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GACtB,IAAI;IAMP;;;;OAIG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI;IAKxD,OAAO,CAAC,2BAA2B;CAKpC;AAED,KAAK,eAAe,GAAG,CAAC,cAAc,SAAS,aAAa,EAC1D,IAAI,EAAE,MAAM,EACZ,mBAAmB,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,EACzD,MAAM,CAAC,EAAE,UAAU,CAAC,cAAc,CAAC,KAChC,cAAc,CAAC;AAEpB,qBAAa,qBAAsB,YAAW,eAAe;aAIzC,IAAI,EAAE,MAAM;IAC5B,OAAO,CAAC,YAAY;IACpB,OAAO,CAAC,QAAQ;IAChB,OAAO,CAAC,WAAW;IANd,KAAK,EAAE,QAAQ,CAAM;gBAGV,IAAI,EAAE,MAAM,EACpB,YAAY,EAAE,MAAM,GAAG,SAAS,EAChC,QAAQ,EAAE,eAAe,EACzB,WAAW,EAAE,eAAe;IAGtC,IAAW,WAAW,uBAErB;IAEM,cAAc,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAKzC,IAAI,CAAC,cAAc,SAAS,aAAa,EAC9C,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,MAAM,CAAC,EAAE,UAAU,CAAC,cAAc,CAAC,GAClC,cAAc;IACV,IAAI,CAAC,cAAc,SAAS,aAAa,EAC9C,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,UAAU,CAAC,cAAc,CAAC,GACjC,cAAc;IAaV,OAAO,CAAC,cAAc,SAAS,aAAa,EACjD,IAAI,EAAE,MAAM,EACZ,WAAW,CAAC,EAAE,MAAM,EACpB,MAAM,CAAC,EAAE,UAAU,CAAC,cAAc,CAAC,GAClC,cAAc;IACV,OAAO,CAAC,cAAc,SAAS,aAAa,EACjD,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,UAAU,CAAC,cAAc,CAAC,GACjC,cAAc;CAYlB"}