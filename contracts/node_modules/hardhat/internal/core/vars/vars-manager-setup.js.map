{"version": 3, "file": "vars-manager-setup.js", "sourceRoot": "", "sources": ["../../../src/internal/core/vars/vars-manager-setup.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,iDAA6C;AAE7C,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,oCAAoC,CAAC,CAAC;AAExD;;GAEG;AACH,MAAa,gBAAiB,SAAQ,0BAAW;IAS/C,YAAY,YAAoB;QAC9B,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAEnD,KAAK,CAAC,YAAY,CAAC,CAAC;QAEpB,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,kCAAkC,GAAG,IAAI,GAAG,EAAE,CAAC;QAEpD,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,IAAI,GAAG,EAAE,CAAC;IACjD,CAAC;IAED,0DAA0D;IAC1D,4GAA4G;IACrG,GAAG,CAAC,GAAW;QACpB,GAAG,CAAC,mCAAmC,GAAG,GAAG,CAAC,CAAC;QAE/C,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAClC;aAAM;YACL,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC7B;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,qEAAqE;IACrE,4GAA4G;IACrG,GAAG,CAAC,GAAW,EAAE,YAAqB;QAC3C,GAAG,CAAC,mCAAmC,GAAG,GAAG,CAAC,CAAC;QAE/C,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAErC,IAAI,aAAa,EAAE;YACjB,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,IAAI,CAAC,kCAAkC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAClD;iBAAM;gBACL,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAClC;SACF;aAAM;YACL,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAC7C;iBAAM;gBACL,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAC7B;SACF;QAED,qDAAqD;QACrD,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC;IAC5C,CAAC;IAEM,yBAAyB;QAC9B,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC7E,CAAC;IAEM,yBAAyB;QAC9B,OAAO,IAAI,CAAC,aAAa,CACvB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,kCAAkC,CACxC,CAAC;IACJ,CAAC;IAEM,oBAAoB;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACnE,CAAC;IAEM,oBAAoB;QACzB,OAAO,IAAI,CAAC,aAAa,CACvB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,6BAA6B,CACnC,CAAC;IACJ,CAAC;IAED,oDAAoD;IACpD,EAAE;IACF,mBAAmB;IACnB,mBAAmB;IACnB,uCAAuC;IACvC,EAAE;IACF,oCAAoC;IACpC,6BAA6B;IACrB,YAAY,CAAC,OAAoB,EAAE,OAAoB;QAC7D,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,aAAa,CACnB,OAAoB,EACpB,OAAoB,EACpB,kBAA+B;QAE/B,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhC,KAAK,MAAM,CAAC,IAAI,kBAAkB,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACnB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACf;SACF;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;CACF;AAnHD,4CAmHC"}