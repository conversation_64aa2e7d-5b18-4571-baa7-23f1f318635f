{"version": 3, "file": "vars-manager.js", "sourceRoot": "", "sources": ["../../../src/internal/core/vars/vars-manager.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA0B;AAC1B,kDAA0B;AAC1B,sCAAyC;AACzC,gDAAwC;AAWxC,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,+BAA+B,CAAC,CAAC;AAEnD,MAAa,WAAW;IAMtB,YAA6B,aAAqB;QAArB,kBAAa,GAAb,aAAa,CAAQ;QALjC,aAAQ,GAAG,WAAW,CAAC;QACvB,oBAAe,GAAG,cAAc,CAAC;QAKhD,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAE9C,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,kBAAE,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEzD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAEM,GAAG,CAAC,GAAW,EAAE,KAAa;QACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAEtB,IAAI,KAAK,KAAK,EAAE,EAAE;YAChB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACzD;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAErC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEM,GAAG,CAAC,GAAW,EAAE,cAAuB,KAAK;QAClD,IAAI,WAAW,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YACxC,OAAO,IAAI,CAAC;SACb;QAED,OAAO,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;IACxC,CAAC;IAEM,GAAG,CACR,GAAW,EACX,YAAqB,EACrB,cAAuB,KAAK;QAE5B,IAAI,WAAW,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YACxC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC5B;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,IAAI,YAAY,CAAC;IAC7D,CAAC;IAEM,UAAU;QACf,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CACpC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CACrC,CAAC;IACJ,CAAC;IAEM,IAAI;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAEM,MAAM,CAAC,GAAW;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAErC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAE1C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE5B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,WAAW,CAAC,GAAW;QAC5B,MAAM,SAAS,GAAG,2BAA2B,CAAC;QAE9C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC1D,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,kBAAE,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YAC1C,gDAAgD;YAChD,GAAG,CACD,kDAAkD,IAAI,CAAC,aAAa,mBAAmB,IAAI,CAAC,QAAQ,GAAG,CACxG,CAAC;YAEF,kBAAE,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBACjE,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,qBAAqB;QAC3B,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,IAAI,EAAE,EAAE;SACT,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAEpC,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;YAC7B,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAEhC,IACE,MAAM,KAAK,SAAS;oBACpB,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,EAC1C;oBACA,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE;wBAC7D,OAAO,EAAE,GAAG;wBACZ,KAAK,EAAE,MAAO;qBACf,CAAC,CAAC;iBACJ;gBAED,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;gBACrD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBAEzB,yFAAyF;gBACzF,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;aACjC;SACF;IACH,CAAC;IAEO,gBAAgB,CAAC,IAAyB;QAChD,2CAA2C;QAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;QAC/B,kBAAE,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1E,CAAC;CACF;AAvID,kCAuIC"}