{"version": 3, "file": "validation.js", "sourceRoot": "", "sources": ["../../../../../src/internal/core/jsonrpc/types/input/validation.ts"], "names": [], "mappings": ";;;AACA,yDAAsD;AAEtD,sDAAkE;AAElE,+EAA+E;AAE/E;;;;;;GAMG;AACH,SAAgB,cAAc,CAG5B,MAAa,EACb,GAAG,KAAa;IAMhB,IAAI,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5C,MAAM,IAAI,8BAAqB,CAC7B,oCAAoC,MAAM,CAAC,MAAM,EAAE,CACpD,CAAC;KACH;IAED,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1C,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE;YAC1B,cAAc,IAAI,CAAC,CAAC;SACrB;aAAM;YACL,MAAM;SACP;KACF;IAED,IAAI,cAAc,KAAK,CAAC,EAAE;QACxB,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;YAClC,MAAM,IAAI,8BAAqB,CAC7B,oBAAoB,KAAK,CAAC,MAAM,sBAAsB,MAAM,CAAC,MAAM,EAAE,CACtE,CAAC;SACH;KACF;SAAM;QACL,IACE,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;YAC5B,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,cAAc,EAC7C;YACA,MAAM,IAAI,8BAAqB,CAC7B,oBAAoB,KAAK,CAAC,MAAM,GAAG,cAAc,QAC/C,KAAK,CAAC,MACR,sBAAsB,MAAM,CAAC,MAAM,EAAE,CACtC,CAAC;SACH;KACF;IAED,MAAM,OAAO,GAAU,EAAE,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1C,IAAI,MAAM,CAAC,MAAM,EAAE,EAAE;YACnB,MAAM,IAAI,8BAAqB,CAC7B,+BAA+B,CAAC,KAAK,2BAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CACnE,IAAI,CACL,EAAE,CACJ,CAAC;SACH;QAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC5B;IAED,OAAO,OAAc,CAAC;AACxB,CAAC;AA5DD,wCA4DC"}