{"version": 3, "file": "vars.js", "sourceRoot": "", "sources": ["../../src/internal/cli/vars.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4DAAoC;AACpC,kDAA0B;AAC1B,2CAAsE;AACtE,qDAA6C;AAC7C,wCAA4C;AAC5C,wEAAmE;AACnE,kEAGuC;AACvC,mDAAqD;AACrD,uDAAoD;AACpD,mCAAgC;AAEhC,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAE/B,KAAK,UAAU,UAAU,CAC9B,eAAyB,EACzB,UAA8B;IAE9B,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GACrC,MAAM,iCAAiC,CAAC,eAAe,CAAC,CAAC;IAE3D,QAAQ,cAAc,CAAC,IAAI,EAAE;QAC3B,KAAK,KAAK;YACR,OAAO,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;QACrD,KAAK,KAAK;YACR,OAAO,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAChC,KAAK,MAAM;YACT,OAAO,IAAI,EAAE,CAAC;QAChB,KAAK,QAAQ;YACX,OAAO,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAChC,KAAK,MAAM;YACT,OAAO,IAAI,EAAE,CAAC;QAChB,KAAK,OAAO;YACV,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC;QAC3B;YACE,OAAO,CAAC,KAAK,CAAC,oBAAU,CAAC,GAAG,CAAC,iBAAiB,cAAc,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACvE,OAAO,CAAC,CAAC,CAAC,aAAa;KAC1B;AACH,CAAC;AAxBD,gCAwBC;AAED,KAAK,UAAU,GAAG,CAAC,GAAW,EAAE,KAAc;IAC5C,MAAM,WAAW,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC;IAEnE,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAE7B,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,MAAM,WAAW,EAAE,CAAC,CAAC,CAAC;IAErD,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;QACxB,OAAO,CAAC,IAAI,CACV,iDAAiD,WAAW,CAAC,cAAc,EAAE,EAAE,CAChF,CAAC;KACH;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,GAAG,CAAC,GAAW;IACtB,MAAM,KAAK,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEtE,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,CAAC,CAAC;KACV;IAED,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,+BAA+B,GAAG,mBAAmB,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,cAAc,EAAE,EAAE,CACvH,CACF,CAAC;IACF,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,IAAI;IACX,MAAM,IAAI,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACnE,MAAM,eAAe,GACnB,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;IAElE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACnB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;YACxB,OAAO,CAAC,IAAI,CACV,+CAA+C,eAAe,EAAE,CACjE,CAAC;SACH;KACF;SAAM;QACL,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;YACxB,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,kDAAkD,eAAe,EAAE,CACpE,CACF,CAAC;SACH;KACF;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,GAAG,CAAC,GAAW;IACtB,MAAM,eAAe,GACnB,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;IAElE,IAAI,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;QAC9D,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;YACxB,OAAO,CAAC,IAAI,CACV,+CAA+C,eAAe,EAAE,CACjE,CAAC;SACH;QACD,OAAO,CAAC,CAAC;KACV;IAED,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,uCAAuC,GAAG,oBAAoB,eAAe,EAAE,CAChF,CACF,CAAC;IAEF,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,IAAI;IACX,OAAO,CAAC,GAAG,CAAC,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC;IAC7E,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,KAAK,CAAC,UAA8B;IAC3C,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAErD,MAAM,gBAAgB,GAAG,IAAI,qCAAgB,CAAC,IAAA,4BAAe,GAAE,CAAC,CAAC;IAEjE,wBAAc,CAAC,iBAAiB,EAAE,CAAC,WAAW,GAAG,gBAAgB,CAAC;IAElE,IAAI;QACF,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAC3D,cAAc,CAAC,UAAU,CAAC,CAAC;KAC5B;IAAC,OAAO,GAAQ,EAAE;QACjB,OAAO,CAAC,KAAK,CACX,oBAAU,CAAC,GAAG,CACZ,iFAAiF,CAClF,CACF,CAAC;QAEF,sFAAsF;QACtF,MAAM,GAAG,CAAC;KACX;IAED,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAElC,OAAO,CAAC,CAAC;AACX,CAAC;AAED,8EAA8E;AAC9E,6FAA6F;AAC7F,qFAAqF;AACrF,SAAS,cAAc,CAAC,UAA8B;IACpD,MAAM,SAAS,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;IAEvD,yFAAyF;IACzF,MAAM,WAAW,GAAQ,MAAM,CAAC;IAChC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAC/B,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAC7C,CAAC;IAEF,MAAM,kBAAkB,GAAG,IAAA,kCAAiB,EAAC,UAAU,CAAC,CAAC;IACzD,IAAA,oCAAmB,EAAC,kBAAkB,CAAC,CAAC;AAC1C,CAAC;AAED,KAAK,UAAU,WAAW;IACxB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,wDAAa,UAAU,GAAC,CAAC;IAEvD,MAAM,QAAQ,GAAsB,MAAM,QAAQ,CAAC,MAAM,CAAC;QACxD,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,cAAc;KACxB,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC,KAAK,CAAC;AACxB,CAAC;AAED,SAAS,eAAe,CAAC,gBAAkC;IACzD,MAAM,cAAc,GAAG,sBAAsB,CAAC;IAE9C,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;IAClE,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;IAElE,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;QACpE,OAAO,CAAC,GAAG,CACT,oBAAU,CAAC,KAAK,CACd,2EAA2E,CAC5E,CACF,CAAC;QACF,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACtC,OAAO;KACR;IAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,OAAO,CAAC,GAAG,CACT,oBAAU,CAAC,IAAI,CACb,GAAG,IAAA,aAAK,EAAC,IAAI,CAAC,yDAAyD,CACxE,CACF,CAAC;QACF,OAAO,CAAC,GAAG,CACT,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,cAAc,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACpE,CAAC;QACF,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;IAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,OAAO,CAAC,GAAG,CACT,oBAAU,CAAC,IAAI,CACb,GAAG,IAAA,aAAK,EAAC,KAAK,CAAC,uDAAuD,CACvE,CACF,CAAC;QACF,OAAO,CAAC,GAAG,CACT,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,cAAc,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACpE,CAAC;QACF,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;IAED,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,mBAAmB,CAAC,gBAAkC;IAC7D,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;IAC5E,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;IAC5E,MAAM,OAAO,GAAG,gBAAgB,CAAC,UAAU,EAAE,CAAC;IAE9C,IACE,sBAAsB,CAAC,MAAM,KAAK,CAAC;QACnC,sBAAsB,CAAC,MAAM,KAAK,CAAC;QACnC,OAAO,CAAC,MAAM,KAAK,CAAC,EACpB;QACA,OAAO;KACR;IAED,OAAO,CAAC,GAAG,CACT,GAAG,oBAAU,CAAC,IAAI,CAAC,GAAG,IAAA,aAAK,EAAC,MAAM,CAAC,sCAAsC,CAAC,EAAE,CAC7E,CAAC;IACF,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;QACrC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;IAED,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;QACrC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;IAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,EAAE,CAAC;KACf;AACH,CAAC;AAED,KAAK,UAAU,iCAAiC,CAAC,eAAyB;IACxE,MAAM,GAAG,GAAG,wBAAc,CAAC,iBAAiB,EAAE,CAAC;IAC/C,GAAG,CAAC,yBAAyB,EAAE,CAAC;IAChC,OAAO,CAAC,0BAA0B,CAAC,CAAC;IACpC,GAAG,CAAC,0BAA0B,EAAE,CAAC;IAEjC,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;IAE9C,MAAM,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;IAC1D,MAAM,iBAAiB,GAAG,GAAG,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;IAE9D,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,GACzC,eAAe,CAAC,sBAAsB,CACpC,eAAe,EACf,eAAe,EACf,iBAAiB,CAClB,CAAC;IAEJ,IAAA,+BAAsB,EACpB,SAAS,KAAK,MAAM,EACpB,4EAA4E,CAC7E,CAAC;IAEF,MAAM,cAAc,GAAG,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAE3E,IAAI,cAAc,KAAK,SAAS,EAAE;QAChC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE;YAChE,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,QAAQ;SACf,CAAC,CAAC;KACJ;IAED,MAAM,aAAa,GAAG,eAAe,CAAC,kBAAkB,CACtD,cAAc,EACd,YAAY,CACb,CAAC;IAEF,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,CAAC;AAC3C,CAAC"}