{"_format": "hh-sol-artifact-1", "contractName": "SafeERC20", "sourceName": "contracts/token/ERC20/utils/SafeERC20.sol", "abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "currentAllowance", "type": "uint256"}, {"internalType": "uint256", "name": "requestedDecrease", "type": "uint256"}], "name": "SafeERC20FailedDecreaseAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220f14534f999eff3ff8188c7c5a10a94fee8b90a996c48faa2874c91fbc12036bd64736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220f14534f999eff3ff8188c7c5a10a94fee8b90a996c48faa2874c91fbc12036bd64736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}