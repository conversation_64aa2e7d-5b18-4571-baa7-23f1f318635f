{"_format": "hh-sol-artifact-1", "contractName": "ERC6909TokenSupply", "sourceName": "contracts/token/ERC6909/extensions/draft-ERC6909TokenSupply.sol", "abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC6909InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC6909InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC6909InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC6909InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "OperatorSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}