{"_format": "hh-sol-artifact-1", "contractName": "ECDSA", "sourceName": "contracts/utils/cryptography/ECDSA.sol", "abi": [{"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220b1584417426ec446135f2ff24669811e405d8a4ad40aa1e08a105a5ee770690e64736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220b1584417426ec446135f2ff24669811e405d8a4ad40aa1e08a105a5ee770690e64736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}