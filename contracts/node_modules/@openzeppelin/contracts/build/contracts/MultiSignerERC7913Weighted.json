{"_format": "hh-sol-artifact-1", "contractName": "MultiSignerERC7913Weighted", "sourceName": "contracts/utils/cryptography/signers/MultiSignerERC7913Weighted.sol", "abi": [{"inputs": [{"internalType": "bytes", "name": "signer", "type": "bytes"}], "name": "MultiSignerERC7913AlreadyExists", "type": "error"}, {"inputs": [{"internalType": "bytes", "name": "signer", "type": "bytes"}], "name": "MultiSignerERC7913InvalidSigner", "type": "error"}, {"inputs": [{"internalType": "bytes", "name": "signer", "type": "bytes"}], "name": "MultiSignerERC7913NonexistentSigner", "type": "error"}, {"inputs": [{"internalType": "uint64", "name": "signers", "type": "uint64"}, {"internalType": "uint64", "name": "threshold", "type": "uint64"}], "name": "MultiSignerERC7913UnreachableThreshold", "type": "error"}, {"inputs": [{"internalType": "bytes", "name": "signer", "type": "bytes"}, {"internalType": "uint64", "name": "weight", "type": "uint64"}], "name": "MultiSignerERC7913WeightedInvalidWeight", "type": "error"}, {"inputs": [], "name": "MultiSignerERC7913WeightedMismatchedLength", "type": "error"}, {"inputs": [], "name": "MultiSignerERC7913ZeroThreshold", "type": "error"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintDowncast", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes", "name": "signers", "type": "bytes"}], "name": "ERC7913SignerAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes", "name": "signers", "type": "bytes"}], "name": "ERC7913SignerRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes", "name": "signer", "type": "bytes"}, {"indexed": false, "internalType": "uint64", "name": "weight", "type": "uint64"}], "name": "ERC7913SignerWeightChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "threshold", "type": "uint64"}], "name": "ERC7913ThresholdSet", "type": "event"}, {"inputs": [], "name": "getSignerCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "start", "type": "uint64"}, {"internalType": "uint64", "name": "end", "type": "uint64"}], "name": "getSigners", "outputs": [{"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "signer", "type": "bytes"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "signer", "type": "bytes"}], "name": "signer<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "threshold", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalWeight", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}