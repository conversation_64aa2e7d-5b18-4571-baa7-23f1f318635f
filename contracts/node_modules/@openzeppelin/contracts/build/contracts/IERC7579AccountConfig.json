{"_format": "hh-sol-artifact-1", "contractName": "IERC7579AccountConfig", "sourceName": "contracts/interfaces/draft-IERC7579.sol", "abi": [{"inputs": [], "name": "accountId", "outputs": [{"internalType": "string", "name": "accountImplementationId", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "encodedMode", "type": "bytes32"}], "name": "supportsExecutionMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}], "name": "supportsModule", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}