{"_format": "hh-sol-artifact-1", "contractName": "AccountERC7579", "sourceName": "contracts/account/extensions/draft-AccountERC7579.sol", "abi": [{"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "AccountUnauthorized", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}], "name": "ERC7579AlreadyInstalledModule", "type": "error"}, {"inputs": [], "name": "ERC7579DecodingError", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}], "name": "ERC7579MismatchedModuleTypeId", "type": "error"}, {"inputs": [{"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "ERC7579MissingFallbackHandler", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}], "name": "ERC7579UninstalledModule", "type": "error"}, {"inputs": [{"internalType": "CallType", "name": "callType", "type": "bytes1"}], "name": "ERC7579UnsupportedCallType", "type": "error"}, {"inputs": [{"internalType": "ExecType", "name": "execType", "type": "bytes1"}], "name": "ERC7579UnsupportedExecType", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}], "name": "ERC7579UnsupportedModuleType", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "OutOfRangeAccess", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "batchExecutionIndex", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "returndata", "type": "bytes"}], "name": "ERC7579TryExecuteFail", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "module", "type": "address"}], "name": "ModuleInstalled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "module", "type": "address"}], "name": "ModuleUninstalled", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "accountId", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "entryPoint", "outputs": [{"internalType": "contract IEntryPoint", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "mode", "type": "bytes32"}, {"internalType": "bytes", "name": "executionCalldata", "type": "bytes"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "mode", "type": "bytes32"}, {"internalType": "bytes", "name": "executionCalldata", "type": "bytes"}], "name": "executeFromExecutor", "outputs": [{"internalType": "bytes[]", "name": "returnData", "type": "bytes[]"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint192", "name": "key", "type": "uint192"}], "name": "getNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}, {"internalType": "bytes", "name": "initData", "type": "bytes"}], "name": "installModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}, {"internalType": "bytes", "name": "additionalContext", "type": "bytes"}], "name": "isModuleInstalled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "hash", "type": "bytes32"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "isValidSignature", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "encodedMode", "type": "bytes32"}], "name": "supportsExecutionMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}], "name": "supportsModule", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}, {"internalType": "bytes", "name": "deInitData", "type": "bytes"}], "name": "uninstallModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bytes", "name": "initCode", "type": "bytes"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "bytes32", "name": "accountGasLimits", "type": "bytes32"}, {"internalType": "uint256", "name": "preVerificationGas", "type": "uint256"}, {"internalType": "bytes32", "name": "gasFees", "type": "bytes32"}, {"internalType": "bytes", "name": "paymasterAndData", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct PackedUserOperation", "name": "userOp", "type": "tuple"}, {"internalType": "bytes32", "name": "userOpHash", "type": "bytes32"}, {"internalType": "uint256", "name": "missingAccountFunds", "type": "uint256"}], "name": "validateUserOp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}