{"_format": "hh-sol-artifact-1", "contractName": "IERC7821", "sourceName": "contracts/interfaces/draft-IERC7821.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "mode", "type": "bytes32"}, {"internalType": "bytes", "name": "executionData", "type": "bytes"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "mode", "type": "bytes32"}], "name": "supportsExecutionMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}