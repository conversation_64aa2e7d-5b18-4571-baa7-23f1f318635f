{"_format": "hh-sol-artifact-1", "contractName": "IERC1820Implementer", "sourceName": "contracts/interfaces/IERC1820Implementer.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "interfaceHash", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "canImplementInterfaceForAddress", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}