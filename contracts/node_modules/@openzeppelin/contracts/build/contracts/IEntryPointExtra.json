{"_format": "hh-sol-artifact-1", "contractName": "IEntryPointExtra", "sourceName": "contracts/account/utils/draft-ERC4337Utils.sol", "abi": [{"inputs": [{"components": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bytes", "name": "initCode", "type": "bytes"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "bytes32", "name": "accountGasLimits", "type": "bytes32"}, {"internalType": "uint256", "name": "preVerificationGas", "type": "uint256"}, {"internalType": "bytes32", "name": "gasFees", "type": "bytes32"}, {"internalType": "bytes", "name": "paymasterAndData", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct PackedUserOperation", "name": "userOp", "type": "tuple"}], "name": "getUserOpHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}