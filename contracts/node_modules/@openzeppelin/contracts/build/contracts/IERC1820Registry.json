{"_format": "hh-sol-artifact-1", "contractName": "IERC1820Registry", "sourceName": "contracts/interfaces/IERC1820Registry.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "interfaceHash", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "implementer", "type": "address"}], "name": "InterfaceImplementerSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newManager", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "_interfaceHash", "type": "bytes32"}], "name": "getInterfaceImplementer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getManager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "implementsERC165Interface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "implementsERC165InterfaceNoCache", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "interfaceName", "type": "string"}], "name": "interfaceHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "_interfaceHash", "type": "bytes32"}, {"internalType": "address", "name": "implementer", "type": "address"}], "name": "setInterfaceImplementer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "newManager", "type": "address"}], "name": "setManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "updateERC165Cache", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}