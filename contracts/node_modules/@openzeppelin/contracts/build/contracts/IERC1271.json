{"_format": "hh-sol-artifact-1", "contractName": "IERC1271", "sourceName": "contracts/interfaces/IERC1271.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "hash", "type": "bytes32"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "isValidSignature", "outputs": [{"internalType": "bytes4", "name": "magicValue", "type": "bytes4"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}