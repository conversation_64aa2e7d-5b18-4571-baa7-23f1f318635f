{"_format": "hh-sol-artifact-1", "contractName": "ERC2771Context", "sourceName": "contracts/metatx/ERC2771Context.sol", "abi": [{"inputs": [{"internalType": "address", "name": "forwarder", "type": "address"}], "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}