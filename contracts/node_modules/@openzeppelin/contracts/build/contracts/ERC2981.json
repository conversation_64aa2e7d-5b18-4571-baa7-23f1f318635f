{"_format": "hh-sol-artifact-1", "contractName": "ERC2981", "sourceName": "contracts/token/common/ERC2981.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "numerator", "type": "uint256"}, {"internalType": "uint256", "name": "denominator", "type": "uint256"}], "name": "ERC2981InvalidDefaultRoyalty", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC2981InvalidDefaultRoyaltyReceiver", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "numerator", "type": "uint256"}, {"internalType": "uint256", "name": "denominator", "type": "uint256"}], "name": "ERC2981InvalidTokenRoyalty", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC2981InvalidTokenRoyaltyReceiver", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "salePrice", "type": "uint256"}], "name": "royaltyInfo", "outputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}