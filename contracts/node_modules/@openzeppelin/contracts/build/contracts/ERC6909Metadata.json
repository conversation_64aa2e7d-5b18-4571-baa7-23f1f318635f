{"_format": "hh-sol-artifact-1", "contractName": "ERC6909Metadata", "sourceName": "contracts/token/ERC6909/extensions/draft-ERC6909Metadata.sol", "abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC6909InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC6909InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC6909InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC6909InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "newDecimals", "type": "uint8"}], "name": "ERC6909DecimalsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "newName", "type": "string"}], "name": "ERC6909NameUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "newSymbol", "type": "string"}], "name": "ERC6909SymbolUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "OperatorSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}