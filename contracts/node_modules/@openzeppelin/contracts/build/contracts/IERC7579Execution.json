{"_format": "hh-sol-artifact-1", "contractName": "IERC7579Execution", "sourceName": "contracts/interfaces/draft-IERC7579.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "mode", "type": "bytes32"}, {"internalType": "bytes", "name": "executionCalldata", "type": "bytes"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "mode", "type": "bytes32"}, {"internalType": "bytes", "name": "executionCalldata", "type": "bytes"}], "name": "executeFromExecutor", "outputs": [{"internalType": "bytes[]", "name": "returnData", "type": "bytes[]"}], "stateMutability": "payable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}