{"_format": "hh-sol-artifact-1", "contractName": "ERC2771<PERSON><PERSON><PERSON><PERSON>", "sourceName": "contracts/metatx/ERC2771Forwarder.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint48", "name": "deadline", "type": "uint48"}], "name": "ERC2771ForwarderExpiredRequest", "type": "error"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}], "name": "ERC2771ForwarderInvalidSigner", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "requestedValue", "type": "uint256"}, {"internalType": "uint256", "name": "msgValue", "type": "uint256"}], "name": "ERC2771ForwarderMismatchedValue", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "address", "name": "forwarder", "type": "address"}], "name": "ERC2771UntrustfulTarget", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "name": "InvalidAccountNonce", "type": "error"}, {"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "signer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "success", "type": "bool"}], "name": "ExecutedForwardRequest", "type": "event"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint48", "name": "deadline", "type": "uint48"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct ERC2771Forwarder.ForwardRequestData", "name": "request", "type": "tuple"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint48", "name": "deadline", "type": "uint48"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct ERC2771Forwarder.ForwardRequestData[]", "name": "requests", "type": "tuple[]"}, {"internalType": "address payable", "name": "refundReceiver", "type": "address"}], "name": "executeBatch", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint48", "name": "deadline", "type": "uint48"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct ERC2771Forwarder.ForwardRequestData", "name": "request", "type": "tuple"}], "name": "verify", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}