{"_format": "hh-sol-artifact-1", "contractName": "ShortStrings", "sourceName": "contracts/utils/ShortStrings.sol", "abi": [{"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220e031f63d8bdbc3a12ab6a5e8c79316f22e301d6402d7517faa8a3bfd041e90c764736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220e031f63d8bdbc3a12ab6a5e8c79316f22e301d6402d7517faa8a3bfd041e90c764736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}