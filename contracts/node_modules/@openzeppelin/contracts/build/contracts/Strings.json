{"_format": "hh-sol-artifact-1", "contractName": "Strings", "sourceName": "contracts/utils/Strings.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "StringsInsufficientHexLength", "type": "error"}, {"inputs": [], "name": "StringsInvalidAddressFormat", "type": "error"}, {"inputs": [], "name": "StringsInvalidChar", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220286809d48abee4169d5b03cd5b9c06455a85650a0ee12351da7c4f6b613a395264736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220286809d48abee4169d5b03cd5b9c06455a85650a0ee12351da7c4f6b613a395264736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}