{"name": "@openzeppelin/contracts", "description": "Secure Smart Contract library for Solidity", "version": "5.4.0", "files": ["**/*.sol", "/build/contracts/*.json", "!/mocks/**/*"], "scripts": {"prepack": "bash ../scripts/prepack.sh", "prepare-docs": "cd ..; npm run prepare-docs"}, "repository": {"type": "git", "url": "https://github.com/OpenZeppelin/openzeppelin-contracts.git"}, "keywords": ["solidity", "ethereum", "smart", "contracts", "security", "zeppelin"], "author": "OpenZeppelin Community <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/OpenZeppelin/openzeppelin-contracts/issues"}, "homepage": "https://openzeppelin.com/contracts/"}