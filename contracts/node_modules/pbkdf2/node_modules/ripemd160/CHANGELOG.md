2.0.1 / 2016-06-22
------------------
- added LICENSE file.

2.0.0 / 2016-04-11
------------------
- rewritten, license change BSD-3 to MIT. [#13][#13]
- stream support [#13][#13]

1.0.1 / 2015-05-05
------------------
- standard formatting

1.0.0 / 2015-01-14
------------------
- updated dev deps
- added more test fixtures
- updated readme with usage, testing, etc
- moved from https://github.com/cryptocoinjs/ripemd160 to https://github.com/crypto-browserify/ripemd160

0.2.1 / 2014-12-31
------------------
- made license clear in `package.json`
- deleted `Makefile`, moved targets to `package.json`
- removed `terst` for `assert`

0.2.0 / 2014-03-09
------------------
* removed bower.json and component.json
* changed 4 spacing to 2
* returns `<PERSON>uffer` type now, input must be <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, or string
* remove deps: `convert-hex` and `convert-string`

0.1.0 / 2013-11-20
------------------
* changed package name
* removed AMD support

0.0.2 / 2013-11-06
------------------
* fixed component.json file

0.0.1 / 2013-11-03
------------------
* initial release

<!--- add streams support, unroll loops [enhancement] -->
[#13]: https://github.com/crypto-browserify/ripemd160/pull/13
<!--- Update all dependencies 🌴 [greenkeeper] -->
[#12]: https://github.com/crypto-browserify/ripemd160/pull/12
<!--- little optimizations [enhancement] -->
[#11]: https://github.com/crypto-browserify/ripemd160/pull/11
<!--- Added LICENSE file to repo -->
[#10]: https://github.com/crypto-browserify/ripemd160/pull/10
<!--- Add LICENCE file -->
[#9]: https://github.com/crypto-browserify/ripemd160/pull/9
<!--- Support streaming [enhancement] -->
[#8]: https://github.com/crypto-browserify/ripemd160/issues/8
<!--- Unroll loops [enhancement] -->
[#7]: https://github.com/crypto-browserify/ripemd160/pull/7
<!--- Use SPDX-standard license naming -->
[#6]: https://github.com/crypto-browserify/ripemd160/pull/6
<!--- License -->
[#5]: https://github.com/crypto-browserify/ripemd160/issues/5
<!--- Add BSD license to package.json -->
[#4]: https://github.com/crypto-browserify/ripemd160/pull/4
<!--- Indicate byte array inputs are supported too -->
[#3]: https://github.com/crypto-browserify/ripemd160/pull/3
<!--- Node-specific implementation -->
[#2]: https://github.com/crypto-browserify/ripemd160/pull/2
<!--- Incorrect name of convert-string dependency in bower.json -->
[#1]: https://github.com/crypto-browserify/ripemd160/pull/1
