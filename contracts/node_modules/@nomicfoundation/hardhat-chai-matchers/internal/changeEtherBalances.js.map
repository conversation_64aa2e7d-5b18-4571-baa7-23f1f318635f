{"version": 3, "file": "changeEtherBalances.js", "sourceRoot": "", "sources": ["../src/internal/changeEtherBalances.ts"], "names": [], "mappings": ";;;AAKA,oCAAuC;AACvC,4CAA8C;AAC9C,4CAA2D;AAC3D,2CAA4D;AAC5D,mCAAuE;AAEvE,SAAgB,0BAA0B,CACxC,SAA+B,EAC/B,SAAyB;IAEzB,SAAS,CAAC,SAAS,CACjB,yCAA6B,EAC7B,UAEE,QAAqC,EACrC,cAAiE,EACjE,OAA8B;QAE9B,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;QACzD,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAoB,CAAC;QACtD,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,GAAG,OAAO,EAAE,CAAC;SACrB;QAED,IAAA,mCAA2B,EACzB,IAAI,EACJ,yCAA6B,EAC7B,SAAS,CACV,CAAC;QAEF,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEnD,MAAM,mBAAmB,GAAG,CAAC,CAAC,aAAa,EAAE,gBAAgB,CAG5D,EAAE,EAAE;YACH,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;YAEzD,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;gBACxC,MAAM,CACJ,cAAc,CAAC,aAAa,CAAC,EAC7B,wFAAwF,EACxF,yFAAyF,CAC1F,CAAC;aACH;iBAAM;gBACL,MAAM,CACJ,aAAa,CAAC,KAAK,CACjB,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,MAAM,KAAK,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAC1D,EACD,GAAG,EAAE;oBACH,MAAM,KAAK,GAAa,EAAE,CAAC;oBAC3B,aAAa,CAAC,OAAO,CAAC,CAAC,MAAc,EAAE,CAAC,EAAE,EAAE;wBAC1C,IAAI,MAAM,KAAK,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC1C,KAAK,CAAC,IAAI,CACR,iCACE,gBAAgB,CAAC,CAAC,CACpB,SAAS,OAAO,CACd,CAAC,GAAG,CAAC,CACN,sCAAsC,cAAc,CACnD,CAAC,CACF,CAAC,QAAQ,EAAE,2BAA2B,MAAM,CAAC,QAAQ,EAAE,MAAM,CAC/D,CAAC;yBACH;oBACH,CAAC,CAAC,CAAC;oBACH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC,EACD,GAAG,EAAE;oBACH,MAAM,KAAK,GAAa,EAAE,CAAC;oBAC3B,aAAa,CAAC,OAAO,CAAC,CAAC,MAAc,EAAE,CAAC,EAAE,EAAE;wBAC1C,IAAI,MAAM,KAAK,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC1C,KAAK,CAAC,IAAI,CACR,iCACE,gBAAgB,CAAC,CAAC,CACpB,SAAS,OAAO,CACd,CAAC,GAAG,CAAC,CACN,0CAA0C,cAAc,CACvD,CAAC,CACF,CAAC,QAAQ,EAAE,kBAAkB,CAC/B,CAAC;yBACH;oBACH,CAAC,CAAC,CAAC;oBACH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC,CACF,CAAC;aACH;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;YAC7C,IAAA,sBAAY,EAAC,QAAQ,CAAC;SACvB,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AA/FD,gEA+FC;AAED,SAAS,aAAa,CACpB,GAAQ,EACR,QAAqC,EACrC,cAAyE;IAEzE,IAAI;QACF,IACE,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC;YAC7B,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EACzC;YACA,MAAM,IAAI,KAAK,CACb,2BAA2B,QAAQ,CAAC,MAAM,+DAA+D,cAAc,CAAC,MAAM,GAAG,CAClI,CAAC;SACH;KACF;IAAC,OAAO,CAAC,EAAE;QACV,uEAAuE;QACvE,oCAAoC;QACpC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACrC,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAEM,KAAK,UAAU,iBAAiB,CACrC,WAA+D,EAC/D,QAAqC,EACrC,OAA8B;IAE9B,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;IAErC,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;IAC1C,IAAA,uBAAe,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IACxC,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;IAE5C,MAAM,aAAa,GAAG,MAAM,IAAA,qBAAW,EAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IACjE,MAAM,cAAc,GAAG,MAAM,IAAA,qBAAW,EAAC,QAAQ,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC;IAEtE,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAE9D,OAAO,aAAa,CAAC,GAAG,CACtB,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAC9D,CAAC;AACJ,CAAC;AAnBD,8CAmBC;AAED,KAAK,UAAU,SAAS,CACtB,QAAqC,EACrC,UAA+B,EAC/B,OAA8B;IAE9B,OAAO,OAAO,CAAC,GAAG,CAChB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QAC7B,IACE,OAAO,EAAE,UAAU,KAAK,IAAI;YAC5B,CAAC,MAAM,IAAA,sBAAY,EAAC,OAAO,CAAC,CAAC,KAAK,UAAU,CAAC,IAAI,EACjD;YACA,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;YAC1C,IAAA,uBAAe,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC;YAC3D,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAClC,MAAM,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC;YAEjC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CACH,CAAC;AACJ,CAAC"}