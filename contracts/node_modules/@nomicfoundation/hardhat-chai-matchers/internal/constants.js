"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.REVERTED_WITHOUT_REASON_MATCHER = exports.REVERTED_WITH_PANIC_MATCHER = exports.REVERTED_WITH_CUSTOM_ERROR_MATCHER = exports.REVERTED_WITH_MATCHER = exports.REVERTED_MATCHER = exports.EMIT_MATCHER = exports.CHANGE_TOKEN_BALANCES_MATCHER = exports.CHANGE_TOKEN_BALANCE_MATCHER = exports.CHANGE_ETHER_BALANCES_MATCHER = exports.CHANGE_ETHER_BALANCE_MATCHER = exports.PREVIOUS_MATCHER_NAME = exports.ASSERTION_ABORTED = void 0;
exports.ASSERTION_ABORTED = "hh-chai-matchers-assertion-aborted";
exports.PREVIOUS_MATCHER_NAME = "previousMatcherName";
exports.CHANGE_ETHER_BALANCE_MATCHER = "changeEtherBalance";
exports.CHANGE_ETHER_BALANCES_MATCHER = "changeEtherBalances";
exports.CHANGE_TOKEN_BALANCE_MATCHER = "changeTokenBalance";
exports.CHANGE_TOKEN_BALANCES_MATCHER = "changeTokenBalances";
exports.EMIT_MATCHER = "emit";
exports.REVERTED_MATCHER = "reverted";
exports.REVERTED_WITH_MATCHER = "revertedWith";
exports.REVERTED_WITH_CUSTOM_ERROR_MATCHER = "revertedWithCustomError";
exports.REVERTED_WITH_PANIC_MATCHER = "revertedWithPanic";
exports.REVERTED_WITHOUT_REASON_MATCHER = "revertedWithoutReason";
//# sourceMappingURL=constants.js.map