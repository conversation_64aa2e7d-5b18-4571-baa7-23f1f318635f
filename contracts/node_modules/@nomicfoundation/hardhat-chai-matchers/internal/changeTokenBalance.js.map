{"version": 3, "file": "changeTokenBalance.js", "sourceRoot": "", "sources": ["../src/internal/changeTokenBalance.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AASA,oCAAuC;AACvC,oDAAkD;AAClD,4CAA8C;AAC9C,2CAGqB;AACrB,mCAAuE;AAevE,SAAgB,yBAAyB,CACvC,SAA+B,EAC/B,SAAyB;IAEzB,SAAS,CAAC,SAAS,CACjB,wCAA4B,EAC5B,UAEE,KAAY,EACZ,OAA6B,EAC7B,aAAmE;QAEnE,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;QAEnD,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,GAAG,OAAO,EAAE,CAAC;SACrB;QAED,IAAA,mCAA2B,EACzB,IAAI,EACJ,wCAA4B,EAC5B,SAAS,CACV,CAAC;QAEF,UAAU,CAAC,KAAK,EAAE,wCAA4B,CAAC,CAAC;QAEhD,MAAM,kBAAkB,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAInE,EAAE,EAAE;YACH,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAExD,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;gBACvC,MAAM,CACJ,aAAa,CAAC,YAAY,CAAC,EAC3B,2BAA2B,gBAAgB,gBAAgB,OAAO,oEAAoE,YAAY,CAAC,QAAQ,EAAE,OAAO,EACpK,2BAA2B,gBAAgB,gBAAgB,OAAO,qEAAqE,YAAY,CAAC,QAAQ,EAAE,OAAO,CACtK,CAAC;aACH;iBAAM;gBACL,MAAM,CACJ,YAAY,KAAK,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,EAC/C,2BAA2B,gBAAgB,gBAAgB,OAAO,kBAAkB,aAAa,CAAC,QAAQ,EAAE,uBAAuB,YAAY,CAAC,QAAQ,EAAE,EAAE,EAC5J,2BAA2B,gBAAgB,gBAAgB,OAAO,sBAAsB,aAAa,CAAC,QAAQ,EAAE,cAAc,CAC/H,CAAC;aACH;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC;YACzC,IAAA,sBAAY,EAAC,OAAO,CAAC;YACrB,mBAAmB,CAAC,KAAK,CAAC;SAC3B,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE5B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;IAEF,SAAS,CAAC,SAAS,CACjB,yCAA6B,EAC7B,UAEE,KAAY,EACZ,QAAqC,EACrC,cAAyE;QAEzE,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;QAEnD,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,GAAG,OAAO,EAAE,CAAC;SACrB;QAED,IAAA,mCAA2B,EACzB,IAAI,EACJ,yCAA6B,EAC7B,SAAS,CACV,CAAC;QAEF,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAE1D,MAAM,qBAAqB,GAAG,OAAO,CAAC,GAAG,CACvC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CACrE,CAAC;QACF,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,sBAAY,CAAC,CAAC,CAAC;QAEjE,MAAM,mBAAmB,GAAG,CAAC,CAC3B,aAAa,EACb,SAAS,EACT,gBAAgB,EACa,EAAE,EAAE;YACjC,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;YAEzD,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;gBACxC,MAAM,CACJ,cAAc,CAAC,aAAa,CAAC,EAC7B,mCAAmC,gBAAgB,4CAA4C,EAC/F,mCAAmC,gBAAgB,6CAA6C,CACjG,CAAC;aACH;iBAAM;gBACL,MAAM,CACJ,aAAa,CAAC,KAAK,CACjB,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CACjE,EACD,4BAA4B,gBAAgB,eAC1C,SACF,iBACE,cACF,uCAAuC,aAAoB,EAAE,EAC7D,4BAA4B,gBAAgB,eAC1C,SACF,qBACE,cACF,8BAA8B,CAC/B,CAAC;aACH;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,qBAAqB;YACrB,gBAAgB;YAChB,mBAAmB,CAAC,KAAK,CAAC;SAC3B,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE7B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AA5ID,8DA4IC;AAED,SAAS,aAAa,CACpB,GAAQ,EACR,KAAY,EACZ,QAAqC,EACrC,cAAyE;IAEzE,IAAI;QACF,UAAU,CAAC,KAAK,EAAE,yCAA6B,CAAC,CAAC;QAEjD,IACE,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC;YAC7B,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EACzC;YACA,MAAM,IAAI,KAAK,CACb,2BAA2B,QAAQ,CAAC,MAAM,+DAA+D,cAAc,CAAC,MAAM,GAAG,CAClI,CAAC;SACH;KACF;IAAC,OAAO,CAAC,EAAE;QACV,uEAAuE;QACvE,oCAAoC;QACpC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACrC,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAED,SAAS,UAAU,CAAC,KAAc,EAAE,MAAc;IAChD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,CAAC,WAAW,IAAI,KAAK,CAAC,EAAE;QAC1E,MAAM,IAAI,KAAK,CACb,yBAAyB,MAAM,6CAA6C,CAC7E,CAAC;KACH;SAAM,IAAK,KAAa,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;QACrE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;KACtE;AACH,CAAC;AAEM,KAAK,UAAU,gBAAgB,CACpC,WAA+D,EAC/D,KAAY,EACZ,OAA6B;IAE7B,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;IACnD,MAAM,GAAG,GAAG,wDAAa,SAAS,GAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;IAEtC,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC;IAErC,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;IAC1C,IAAA,uBAAe,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IACxC,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;IAE5C,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE;QACtD,SAAS,CAAC,SAAS;QACnB,KAAK;KACN,CAAC,CAAC;IAEH,IAAA,cAAM,EACJ,KAAK,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAC/B,KAAK,EACL,sCAAsC,CACvC,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,IAAA,sBAAY,EAAC,OAAO,CAAC,CAAC;IAE5C,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;QAClD,QAAQ,EAAE,aAAa;KACxB,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;QACnD,QAAQ,EAAE,aAAa,GAAG,CAAC;KAC5B,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;AACvD,CAAC;AArCD,4CAqCC;AAED,IAAI,sBAAsB,GAA2B,EAAE,CAAC;AACxD;;;;GAIG;AACH,KAAK,UAAU,mBAAmB,CAAC,KAAY;IAC7C,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;IAC9C,IAAI,sBAAsB,CAAC,YAAY,CAAC,KAAK,SAAS,EAAE;QACtD,IAAI,gBAAgB,GAAG,aAAa,YAAY,GAAG,CAAC;QACpD,IAAI;YACF,gBAAgB,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;SACzC;QAAC,OAAO,CAAC,EAAE;YACV,IAAI;gBACF,gBAAgB,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;aACvC;YAAC,OAAO,EAAE,EAAE,GAAE;SAChB;QAED,sBAAsB,CAAC,YAAY,CAAC,GAAG,gBAAgB,CAAC;KACzD;IAED,OAAO,sBAAsB,CAAC,YAAY,CAAC,CAAC;AAC9C,CAAC;AAED,qBAAqB;AACrB,SAAgB,2BAA2B;IACzC,sBAAsB,GAAG,EAAE,CAAC;AAC9B,CAAC;AAFD,kEAEC"}