{"version": 3, "file": "emit.js", "sourceRoot": "", "sources": ["../src/internal/emit.ts"], "names": [], "mappings": ";;;;;;AAIA,+BAAsC;AACtC,gDAAwB;AAExB,oCAAuC;AACvC,2CAA8D;AAC9D,qCAA6D;AAC7D,mCAIiB;AAKJ,QAAA,WAAW,GAAG,qBAAqB,CAAC;AAEjD,KAAK,UAAU,yBAAyB,CACtC,EAA+C,EAC/C,QAAkB;IAElB,IAAI,IAAmB,CAAC;IACxB,IAAI,EAAE,YAAY,OAAO,EAAE;QACzB,CAAC,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;KACvB;SAAM,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;QACjC,IAAI,GAAG,EAAE,CAAC;KACX;SAAM;QACL,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;KACjB;IACD,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC,CAAC;KACrE;IACD,OAAO,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED,SAAgB,WAAW,CACzB,SAA+B,EAC/B,SAAyB;IAEzB,SAAS,CAAC,SAAS,CACjB,wBAAY,EACZ,UAEE,QAAkB,EAClB,SAAiB,EACjB,GAAG,IAAW;QAEd,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QAErB,IAAA,mCAA2B,EAAC,IAAI,EAAE,wBAAY,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAEjE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEnE,MAAM,SAAS,GAAG,CAAC,OAAmC,EAAE,EAAE;YACxD,gEAAgE;YAChE,2CAA2C;YAC3C,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,6BAAiB,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO;aACR;YAED,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE/C,IAAI,aAAa,GAAyB,IAAI,CAAC;YAC/C,IAAI;gBACF,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;aAC5D;YAAC,OAAO,CAAU,EAAE;gBACnB,IAAI,CAAC,YAAY,SAAS,EAAE;oBAC1B,MAAM,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvD,MAAM,IAAI,qBAAc,CAAC,YAAY,CAAC,CAAC;iBACxC;aACF;YAED,IAAI,aAAa,KAAK,IAAI,EAAE;gBAC1B,MAAM,IAAI,qBAAc,CACtB,UAAU,SAAS,iCAAiC,CACrD,CAAC;aACH;YAED,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC;YACtC,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC;YACxC,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;gBACvC,MAAM,IAAI,0CAAiC,CACzC,wCAAwC,CACzC,CAAC;aACH;YAED,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,MAAM,IAAI,KAAK,CACb,gIAAgI,CACjI,CAAC;aACH;YAED,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;iBACrB,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBAC3C,MAAM,CACL,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,CACrE,CAAC;YAEJ,MAAM,CACJ,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EACpB,mBAAmB,SAAS,gCAAgC,EAC5D,mBAAmB,SAAS,iCAAiC,CAC9D,CAAC;YACF,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7C,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;YACvC,gEAAgE;YAChE,2CAA2C;YAC3C,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,6BAAiB,CAAC,KAAK,IAAI,EAAE;gBACpD,OAAO;aACR;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACjE,MAAM,IAAI,0CAAiC,CACzC,4CAA4C,CAC7C,CAAC;aACH;YAED,OAAO,yBAAyB,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CACjE,CAAC,OAAO,EAAE,EAAE;gBACV,IAAA,uBAAe,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;gBACpC,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;YAC5B,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAW,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AAvGD,kCAuGC;AAEM,KAAK,UAAU,YAAY,CAChC,OAAY,EACZ,SAA+B,EAC/B,SAAyB,EACzB,YAAmB,EACnB,IAAU;IAEV,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,8BAA8B;IACrD,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAE1C,wBAAwB,CACtB,OAAO,EACP,SAAS,EACT,SAAS,EACT,YAAY,EACZ,OAAO,CAAC,IAAI,EACZ,MAAM,EACN,IAAI,CACL,CAAC;AACJ,CAAC;AAnBD,oCAmBC;AAED,MAAM,wBAAwB,GAAG,CAC/B,OAAY,EACZ,SAA+B,EAC/B,SAAyB,EACzB,YAAmB,EACnB,IAAW,EACX,MAAsB,EACtB,IAAU,EACV,EAAE;IACF,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACvD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,MAAM,SAAS,GACb,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,SACrC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,IAAA,uBAAe,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAExC,OAAO,IAAA,6BAAqB,EAC1B,SAAS,EACT,YAAY,EACZ,SAAS,CAAC,IAAI,EACd,IAAI,SAAS,SAAS,EACtB,OAAO,EACP,MAAM,EACN,IAAI,CACL,CAAC;KACH;IACD,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE;QACxB,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM;SACP;aAAM;YACL,IAAI;gBACF,MAAM,SAAS,GACb,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,SACrC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACxB,IAAA,uBAAe,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAExC,IAAA,6BAAqB,EACnB,SAAS,EACT,YAAY,EACZ,SAAS,CAAC,IAAI,EACd,IAAI,SAAS,SAAS,EACtB,OAAO,EACP,MAAM,EACN,IAAI,CACL,CAAC;gBACF,OAAO;aACR;YAAC,MAAM,GAAE;SACX;KACF;IAED,MAAM,CACJ,KAAK,EACL,4BAA4B,cAAI,CAAC,OAAO,CACtC,YAAY,CACb,qCACC,OAAO,CAAC,IAAI,CAAC,MACf,aAAa,SAAS,UAAU,CACjC,CAAC;AACJ,CAAC,CAAC"}