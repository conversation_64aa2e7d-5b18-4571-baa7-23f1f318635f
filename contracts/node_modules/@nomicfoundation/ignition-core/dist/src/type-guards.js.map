{"version": 3, "file": "type-guards.js", "sourceRoot": "", "sources": ["../../src/type-guards.ts"], "names": [], "mappings": ";;;AACA,2CAqBwB;AAExB,SAAS,gBAAgB,CACvB,OAA+B,EAC/B,KAAa;IAEb,gDAAgD;IAChD,sBAAsB;IACtB,sBAAsB;IACtB,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAC3B,IAAI,GAAG,KAAK,SAAS,EAAE;QACrB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC;AAChC,CAAC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,SAAkB;IAC/C,OAAO,CACL,OAAO,SAAS,KAAK,QAAQ;QAC7B,SAAS,KAAK,IAAI;QAClB,cAAc,IAAI,SAAS;QAC3B,UAAU,IAAI,SAAS;QACvB,KAAK,IAAI,SAAS;QAClB,gBAAgB,IAAI,SAAS;QAC7B,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ;QAC1C,OAAO,SAAS,CAAC,QAAQ,KAAK,QAAQ;QACtC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;QAC5B,OAAO,SAAS,CAAC,cAAc,KAAK,QAAQ,CAC7C,CAAC;AACJ,CAAC;AAbD,wCAaC;AAED;;;;GAIG;AACH,SAAgB,YAAY,CAAC,SAAkB;IAC7C,OAAO,CACL,OAAO,SAAS,KAAK,QAAQ,IAAI,gBAAgB,CAAC,mBAAU,EAAE,SAAS,CAAC,CACzE,CAAC;AACJ,CAAC;AAJD,oCAIC;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAC,SAAkB;IACzC,OAAO,CACL,OAAO,SAAS,KAAK,QAAQ;QAC7B,SAAS,KAAK,IAAI;QAClB,MAAM,IAAI,SAAS;QACnB,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAC7B,CAAC;AACJ,CAAC;AAPD,4BAOC;AAED;;;;GAIG;AACH,SAAgB,gBAAgB,CAC9B,MAAc;IAEd,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,mBAAU,CAAC,kCAAkC,CAAC;QACnD,KAAK,mBAAU,CAAC,mBAAmB,CAAC;QACpC,KAAK,mBAAU,CAAC,iCAAiC,CAAC;QAClD,KAAK,mBAAU,CAAC,kBAAkB,CAAC;QACnC,KAAK,mBAAU,CAAC,0BAA0B,CAAC;QAC3C,KAAK,mBAAU,CAAC,WAAW;YACzB,OAAO,IAAI,CAAC;QAEd;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAfD,4CAeC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB,CACtC,MAAc;IAEd,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,mBAAU,CAAC,kCAAkC,CAAC;QACnD,KAAK,mBAAU,CAAC,mBAAmB,CAAC;QACpC,KAAK,mBAAU,CAAC,0BAA0B,CAAC;QAC3C,KAAK,mBAAU,CAAC,WAAW;YACzB,OAAO,IAAI,CAAC;QAEd;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAbD,4DAaC;AAED;;;;GAIG;AACH,SAAgB,yBAAyB,CACvC,MAAc;IAEd,OAAO,CACL,gBAAgB,CAAC,MAAM,CAAC;QACxB,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,WAAW;QACtC,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,mBAAmB,CAC/C,CAAC;AACJ,CAAC;AARD,8DAQC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAClC,MAAc;IAEd,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,aAAa;QACxC,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,WAAW,CACvC,CAAC;AACJ,CAAC;AAPD,oDAOC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CACrC,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,WAAW,CAAC;AAChD,CAAC;AAJD,0DAIC;AAED;;;;GAIG;AACH,SAAgB,0BAA0B,CACxC,SAAkB;IAElB,OAAO,CACL,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,KAAK,mBAAU,CAAC,oBAAoB,CAC1E,CAAC;AACJ,CAAC;AAND,gEAMC;AAED;;;;GAIG;AACH,SAAgB,yBAAyB,CACvC,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,mBAAmB,CAAC;AACxD,CAAC;AAJD,8DAIC;AAED;;;;GAIG;AACH,SAAgB,+BAA+B,CAC7C,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,kCAAkC,CAAC;AACvE,CAAC;AAJD,0EAIC;AAED;;;;GAIG;AACH,SAAgB,kCAAkC,CAChD,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,mBAAmB,CAAC;AACxD,CAAC;AAJD,gFAIC;AAED;;;;GAIG;AACH,SAAgB,8BAA8B,CAC5C,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,iCAAiC,CAAC;AACtE,CAAC;AAJD,wEAIC;AAED;;;;GAIG;AACH,SAAgB,iCAAiC,CAC/C,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,kBAAkB,CAAC;AACvD,CAAC;AAJD,8EAIC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CACrC,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,0BAA0B,CAAC;AAC/D,CAAC;AAJD,0DAIC;AAED;;;;GAIG;AACH,SAAgB,0BAA0B,CACxC,MAAc;IAEd,OAAO,MAAM,CAAC,IAAI,KAAK,mBAAU,CAAC,WAAW,CAAC;AAChD,CAAC;AAJD,gEAIC;AAED;;;;GAIG;AACH,SAAgB,gBAAgB,CAC9B,SAAkB;IAElB,MAAM,eAAe,GAAG;QACtB,mBAAU,CAAC,kCAAkC;QAC7C,mBAAU,CAAC,mBAAmB;QAC9B,mBAAU,CAAC,iCAAiC;QAC5C,mBAAU,CAAC,kBAAkB;KAC9B,CAAC;IAEF,OAAO,CACL,OAAO,SAAS,KAAK,QAAQ;QAC7B,eAAe,CAAC,QAAQ,CAAC,SAAuB,CAAC,CAClD,CAAC;AACJ,CAAC;AAdD,4CAcC;AAED;;;;GAIG;AACH,SAAgB,kBAAkB,CAChC,MAAc;IAEd,OAAO,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC;AAJD,gDAIC;AAED;;;;GAIG;AACH,SAAgB,qCAAqC,CACnD,CAAS;IAWT,OAAO,CACL,CAAC,uBAAuB,CAAC,CAAC,CAAC;QAC3B,CAAC,yBAAyB,CAAC,CAAC,CAAC;QAC7B,CAAC,uBAAuB,CAAC,CAAC,CAAC;QAC3B,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAC/B,CAAC;AACJ,CAAC;AAlBD,sFAkBC;AAED;;;;GAIG;AACH,SAAgB,kBAAkB,CAChC,SAAkB;IAElB,OAAO,CACL,OAAO,SAAS,KAAK,QAAQ;QAC7B,gBAAgB,CAAC,yBAAgB,EAAE,SAAS,CAAC,CAC9C,CAAC;AACJ,CAAC;AAPD,gDAOC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,SAAkB;IAC/C,OAAO,CACL,OAAO,SAAS,KAAK,QAAQ;QAC7B,SAAS,KAAK,IAAI;QAClB,MAAM,IAAI,SAAS;QACnB,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CACnC,CAAC;AACJ,CAAC;AAPD,wCAOC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CACnC,SAAkB;IAElB,OAAO,CACL,cAAc,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,KAAK,yBAAgB,CAAC,OAAO,CACzE,CAAC;AACJ,CAAC;AAND,sDAMC;AAED;;;;GAIG;AACH,SAAgB,6BAA6B,CAC3C,SAAkB;IAElB,OAAO,CACL,cAAc,CAAC,SAAS,CAAC;QACzB,SAAS,CAAC,IAAI,KAAK,yBAAgB,CAAC,gBAAgB,CACrD,CAAC;AACJ,CAAC;AAPD,sEAOC"}