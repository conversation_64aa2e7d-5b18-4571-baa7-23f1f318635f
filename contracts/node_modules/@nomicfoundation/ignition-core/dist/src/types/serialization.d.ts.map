{"version": 3, "file": "serialization.d.ts", "sourceRoot": "", "sources": ["../../../src/types/serialization.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAEtC;;;;GAIG;AACH,MAAM,WAAW,gBAAgB;IAC/B,KAAK,EAAE,QAAQ,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;CACf;AAED;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAClC,MAAM,GACN,gBAAgB,GAChB,MAAM,GACN,OAAO,GACP,WAAW,GACX,sBAAsB,CAAC;AAE3B;;;;GAIG;AACH,MAAM,MAAM,sBAAsB,GAC9B,0BAA0B,GAC1B,sBAAsB,EAAE,GACxB;IAAE,CAAC,KAAK,EAAE,MAAM,GAAG,sBAAsB,CAAA;CAAE,CAAC;AAEhD;;;;;GAKG;AACH,MAAM,WAAW,WAAW;IAC1B,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,aAAa,CAAC;CACtB;AAED;;;;;GAKG;AACH,MAAM,WAAW,WAAW;IAC1B,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,aAAa,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,WAAW,oBAAoB;IACnC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,UAAU,CAAC;IACjB,YAAY,EAAE,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;IAC/C,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED;;;;GAIG;AACH,MAAM,WAAW,uCACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,kCAAkC,CAAC;IACpD,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,EAAE,sBAAsB,EAAE,CAAC;IAC1C,SAAS,EAAE,mBAAmB,CAAC;IAC/B,KAAK,EAAE,gBAAgB,GAAG,qCAAqC,GAAG,WAAW,CAAC;IAC9E,IAAI,EAAE,MAAM,GAAG,6BAA6B,GAAG,SAAS,CAAC;CAC1D;AAED;;;;GAIG;AACH,MAAM,WAAW,0CACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,mBAAmB,CAAC;IACrC,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,EAAE,sBAAsB,EAAE,CAAC;IAC1C,QAAQ,EAAE,QAAQ,CAAC;IACnB,SAAS,EAAE,mBAAmB,CAAC;IAC/B,KAAK,EAAE,gBAAgB,GAAG,qCAAqC,GAAG,WAAW,CAAC;IAC9E,IAAI,EAAE,MAAM,GAAG,6BAA6B,GAAG,SAAS,CAAC;CAC1D;AAED;;;;GAIG;AACH,MAAM,WAAW,sCACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,iCAAiC,CAAC;IACnD,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,mBAAmB,CAAC;IAC/B,IAAI,EAAE,MAAM,GAAG,6BAA6B,GAAG,SAAS,CAAC;CAC1D;AAED;;;;GAIG;AACH,MAAM,WAAW,yCACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,kBAAkB,CAAC;IACpC,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,QAAQ,CAAC;IACnB,SAAS,EAAE,mBAAmB,CAAC;IAC/B,IAAI,EAAE,MAAM,GAAG,6BAA6B,GAAG,SAAS,CAAC;CAC1D;AAED;;;;GAIG;AACH,MAAM,WAAW,iCACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,aAAa,CAAC;IAC/B,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,WAAW,CAAC;IACtB,IAAI,EAAE,sBAAsB,EAAE,CAAC;IAC/B,KAAK,EAAE,gBAAgB,GAAG,qCAAqC,GAAG,WAAW,CAAC;IAC9E,IAAI,EAAE,MAAM,GAAG,6BAA6B,GAAG,SAAS,CAAC;CAC1D;AAED;;;;GAIG;AACH,MAAM,WAAW,+BAAgC,SAAQ,oBAAoB;IAC3E,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC;IAC7B,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,WAAW,CAAC;IACtB,IAAI,EAAE,sBAAsB,EAAE,CAAC;IAC/B,WAAW,EAAE,MAAM,GAAG,MAAM,CAAC;IAC7B,IAAI,EAAE,MAAM,GAAG,6BAA6B,GAAG,SAAS,CAAC;CAC1D;AAED;;;;GAIG;AACH,MAAM,WAAW,uCACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,oBAAoB,CAAC;IACtC,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,WAAW,CAAC;IACtB,IAAI,EAAE,sBAAsB,EAAE,CAAC;CAChC;AAED;;;;GAIG;AACH,MAAM,WAAW,+BAAgC,SAAQ,oBAAoB;IAC3E,IAAI,EAAE,UAAU,CAAC,0BAA0B,CAAC;IAC5C,YAAY,EAAE,MAAM,CAAC;IACrB,OAAO,EAAE,MAAM,GAAG,WAAW,GAAG,qCAAqC,CAAC;CACvE;AAED;;;;GAIG;AACH,MAAM,WAAW,kCACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC;IAC7B,YAAY,EAAE,MAAM,CAAC;IACrB,OAAO,EAAE,MAAM,GAAG,WAAW,GAAG,qCAAqC,CAAC;IACtE,QAAQ,EAAE,QAAQ,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,iCACf,SAAQ,oBAAoB;IAC5B,IAAI,EAAE,UAAU,CAAC,mBAAmB,CAAC;IACrC,gBAAgB,EAAE,WAAW,CAAC;IAC9B,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,GAAG,MAAM,CAAC;IAC7B,OAAO,EAAE,WAAW,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,wBAAyB,SAAQ,oBAAoB;IACpE,IAAI,EAAE,UAAU,CAAC,SAAS,CAAC;IAC3B,EAAE,EACE,MAAM,GACN,WAAW,GACX,qCAAqC,GACrC,6BAA6B,CAAC;IAClC,KAAK,EAAE,gBAAgB,GAAG,qCAAqC,CAAC;IAChE,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS,CAAC;IACvC,IAAI,EAAE,MAAM,GAAG,6BAA6B,GAAG,SAAS,CAAC;CAC1D;AAED;;;;GAIG;AACH,MAAM,MAAM,sBAAsB,GAC9B,6BAA6B,GAC7B,qCAAqC,CAAC;AAE1C;;;;GAIG;AACH,MAAM,WAAW,6BAA6B;IAC5C,KAAK,EAAE,qBAAqB,CAAC;IAC7B,YAAY,EAAE,MAAM,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,WAAW,qCAAqC;IACpD,KAAK,EAAE,6BAA6B,CAAC;IACrC,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,MAAM,GAAG,SAAS,CAAC;CAClC;AAID;;;;GAIG;AACH,MAAM,WAAW,wBAAwB;IACvC,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE;QACP,CAAC,GAAG,EAAE,MAAM,GAAG,2BAA2B,CAAC;KAC5C,CAAC;CACH;AAED;;;;;GAKG;AACH,MAAM,WAAW,2BAA2B;IAC1C,EAAE,EAAE,MAAM,CAAC;IACX,UAAU,EAAE,WAAW,EAAE,CAAC;IAC1B,OAAO,EAAE,gBAAgB,EAAE,CAAC;IAC5B,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;CACpD;AAED;;;;;GAKG;AACH,MAAM,MAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;AAE5E;;;;GAIG;AACH,MAAM,MAAM,gBAAgB,GACxB,uCAAuC,GACvC,0CAA0C,GAC1C,sCAAsC,GACtC,yCAAyC,GACzC,iCAAiC,GACjC,+BAA+B,GAC/B,uCAAuC,GACvC,+BAA+B,GAC/B,kCAAkC,GAClC,iCAAiC,GACjC,wBAAwB,CAAC"}