{"version": 3, "file": "module.d.ts", "sourceRoot": "", "sources": ["../../../src/types/module.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAE3C;;;;;GAKG;AACH,MAAM,MAAM,gBAAgB,GACxB,MAAM,GACN,MAAM,GACN,MAAM,GACN,OAAO,GACP,cAAc,CAAC,MAAM,CAAC,GACtB,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,GACxC,uBAAuB,GACvB,YAAY,CAAC;AAEjB;;;;GAIG;AACH,MAAM,MAAM,YAAY,GACpB,gBAAgB,GAChB,YAAY,EAAE,GACd;IAAE,CAAC,KAAK,EAAE,MAAM,GAAG,YAAY,CAAA;CAAE,CAAC;AAEtC;;;;GAIG;AACH,oBAAY,UAAU;IACpB,kCAAkC,uCAAuC;IACzE,mBAAmB,wBAAwB;IAC3C,iCAAiC,sCAAsC;IACvE,kBAAkB,uBAAuB;IACzC,aAAa,kBAAkB;IAC/B,WAAW,gBAAgB;IAC3B,oBAAoB,yBAAyB;IAC7C,0BAA0B,+BAA+B;IACzD,WAAW,gBAAgB;IAC3B,mBAAmB,wBAAwB;IAC3C,SAAS,cAAc;CACxB;AAED;;;;GAIG;AACH,MAAM,MAAM,MAAM,GACd,qCAAqC,CAAC,MAAM,CAAC,GAC7C,wBAAwB,GACxB,oCAAoC,CAAC,MAAM,CAAC,GAC5C,uBAAuB,GACvB,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,GAClC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,GACxC,6BAA6B,CAAC,MAAM,CAAC,GACrC,gBAAgB,GAChB,uBAAuB,GACvB,cAAc,CAAC;AAEnB;;;;;GAKG;AACH,MAAM,MAAM,cAAc,CAAC,aAAa,SAAS,MAAM,IACnD,qCAAqC,CAAC,aAAa,CAAC,GACpD,wBAAwB,GACxB,oCAAoC,CAAC,aAAa,CAAC,GACnD,uBAAuB,GACvB,6BAA6B,CAAC,aAAa,CAAC,GAC5C,gBAAgB,CAAC;AAErB;;;;;GAKG;AACH,MAAM,MAAM,sBAAsB,CAAC,aAAa,SAAS,MAAM,IAC3D,qCAAqC,CAAC,aAAa,CAAC,GACpD,wBAAwB,GACxB,6BAA6B,CAAC,aAAa,CAAC,GAC5C,gBAAgB,CAAC;AAErB;;;;GAIG;AACH,MAAM,MAAM,gBAAgB,CAAC,aAAa,SAAS,MAAM,IACrD,qCAAqC,CAAC,aAAa,CAAC,GACpD,wBAAwB,GACxB,oCAAoC,CAAC,aAAa,CAAC,GACnD,uBAAuB,CAAC;AAE5B;;;;GAIG;AACH,MAAM,MAAM,kBAAkB,CAC5B,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM,IAE1B,kBAAkB,CAAC,aAAa,EAAE,aAAa,CAAC,GAChD,gBAAgB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAEnD;;;;GAIG;AACH,MAAM,MAAM,uBAAuB,GAC/B,cAAc,CAAC,MAAM,CAAC,GACtB,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,CAAC;AAE5B;;;;GAIG;AACH,MAAM,WAAW,qCAAqC,CACpD,aAAa,SAAS,MAAM;IAE5B,IAAI,EAAE,UAAU,CAAC,kCAAkC,CAAC;IACpD,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,YAAY,EAAE,aAAa,CAAC;IAC5B,eAAe,EAAE,YAAY,EAAE,CAAC;IAChC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IAClD,KAAK,EACD,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,CAAC;IAC5B,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAAC;CAChD;AAED;;;;;GAKG;AACH,MAAM,WAAW,wBAAwB,CAAC,IAAI,SAAS,GAAG,GAAG,GAAG;IAC9D,IAAI,EAAE,UAAU,CAAC,mBAAmB,CAAC;IACrC,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzB,eAAe,EAAE,YAAY,EAAE,CAAC;IAChC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IAClD,KAAK,EACD,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,CAAC;IAC5B,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAAC;CAChD;AAED;;;;GAIG;AACH,MAAM,WAAW,oCAAoC,CACnD,YAAY,SAAS,MAAM;IAE3B,IAAI,EAAE,UAAU,CAAC,iCAAiC,CAAC;IACnD,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,YAAY,EAAE,YAAY,CAAC;IAC3B,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IAClD,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAAC;CAChD;AAED;;;;;GAKG;AACH,MAAM,WAAW,uBAAuB,CAAC,IAAI,SAAS,GAAG,GAAG,GAAG;IAC7D,IAAI,EAAE,UAAU,CAAC,kBAAkB,CAAC;IACpC,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzB,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IAClD,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAAC;CAChD;AAED;;;;GAIG;AACH,MAAM,WAAW,kBAAkB,CACjC,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM;IAE5B,IAAI,EAAE,UAAU,CAAC,aAAa,CAAC;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;IACxC,YAAY,EAAE,aAAa,CAAC;IAC5B,IAAI,EAAE,YAAY,EAAE,CAAC;IACrB,KAAK,EACD,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,CAAC;IAC5B,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAAC;CAChD;AAED;;;;GAIG;AACH,MAAM,WAAW,gBAAgB,CAC/B,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM;IAE5B,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;IACxC,YAAY,EAAE,aAAa,CAAC;IAC5B,WAAW,EAAE,MAAM,GAAG,MAAM,CAAC;IAC7B,IAAI,EAAE,YAAY,EAAE,CAAC;IACrB,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAAC;CAChD;AAED;;;;GAIG;AACH,MAAM,WAAW,wBAAwB,CACvC,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM;IAE5B,IAAI,EAAE,UAAU,CAAC,oBAAoB,CAAC;IACtC,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;IACxC,YAAY,EAAE,aAAa,CAAC;IAC5B,IAAI,EAAE,YAAY,EAAE,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,WAAW,6BAA6B,CAAC,aAAa,SAAS,MAAM;IACzE,IAAI,EAAE,UAAU,CAAC,0BAA0B,CAAC;IAC5C,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,YAAY,EAAE,aAAa,CAAC;IAC5B,OAAO,EACH,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,CAAC;CACzC;AAED;;;;;GAKG;AACH,MAAM,WAAW,gBAAgB,CAAC,IAAI,SAAS,GAAG,GAAG,GAAG;IACtD,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,YAAY,EAAE,MAAM,CAAC;IACrB,OAAO,EACH,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,CAAC;IACxC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CAC1B;AAED;;;;;GAKG;AACH,MAAM,WAAW,uBAAuB;IACtC,IAAI,EAAE,UAAU,CAAC,mBAAmB,CAAC;IACrC,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,gBAAgB,EACZ,qCAAqC,CAAC,MAAM,CAAC,GAC7C,wBAAwB,GACxB,cAAc,GACd,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACvC,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,GAAG,MAAM,CAAC;IAC7B,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAChC,UAAU,EAAE,MAAM,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,UAAU,CAAC,SAAS,CAAC;IAC3B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,cAAc,CAAC;IACvB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAC3C,EAAE,EACE,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,GACnC,mBAAmB,CAAC;IACxB,KAAK,EAAE,MAAM,GAAG,2BAA2B,CAAC,MAAM,CAAC,CAAC;IACpD,IAAI,EAAE,MAAM,GAAG,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC;IACpE,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAAC;CAChD;AAED;;;;GAIG;AACH,MAAM,MAAM,yBAAyB,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;AAE3E;;;;GAIG;AACH,MAAM,MAAM,qBAAqB,GAC7B,yBAAyB,GACzB,qBAAqB,EAAE,GACvB;IAAE,CAAC,KAAK,EAAE,MAAM,GAAG,qBAAqB,CAAA;CAAE,CAAC;AAE/C;;;;GAIG;AACH,MAAM,MAAM,mBAAmB,GAAG,qBAAqB,GAAG,mBAAmB,CAAC;AAE9E;;;;GAIG;AACH,oBAAY,gBAAgB;IAC1B,OAAO,YAAY;IACnB,gBAAgB,qBAAqB;CACtC;AAED;;;;GAIG;AACH,MAAM,MAAM,YAAY,GACpB,mBAAmB,GACnB,2BAA2B,CAAC,mBAAmB,CAAC,CAAC;AAErD;;;;GAIG;AACH,MAAM,WAAW,mBAAmB;IAClC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC;IAC/B,YAAY,EAAE,MAAM,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,WAAW,2BAA2B,CAC1C,UAAU,SAAS,mBAAmB;IAEtC,IAAI,EAAE,gBAAgB,CAAC,gBAAgB,CAAC;IACxC,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,UAAU,GAAG,SAAS,CAAC;CACtC;AAED;;;;GAIG;AACH,MAAM,WAAW,gBAAgB;IAC/B,CAAC,aAAa,EAAE,MAAM,GAAG,qBAAqB,CAAC;CAChD;AAED;;;;GAIG;AACH,MAAM,WAAW,oBAAoB,CAAC,aAAa,SAAS,MAAM;IAChE,CAAC,IAAI,EAAE,MAAM,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;CAC/C;AAED;;;;GAIG;AACH,MAAM,WAAW,cAAc,CAC7B,SAAS,SAAS,MAAM,GAAG,MAAM,EACjC,aAAa,SAAS,MAAM,GAAG,MAAM,EACrC,sBAAsB,SAAS,oBAAoB,CAAC,aAAa,CAAC,GAAG,oBAAoB,CAAC,aAAa,CAAC;IAExG,EAAE,EAAE,SAAS,CAAC;IACd,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IACrB,UAAU,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC;IAChC,OAAO,EAAE,sBAAsB,CAAC;CACjC"}