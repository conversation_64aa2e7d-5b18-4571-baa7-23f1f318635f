{"version": 3, "file": "module-builder.d.ts", "sourceRoot": "", "sources": ["../../../src/types/module-builder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,EACL,mBAAmB,EACnB,uBAAuB,EACvB,YAAY,EACZ,sBAAsB,EACtB,gBAAgB,EAChB,kBAAkB,EAClB,wBAAwB,EACxB,cAAc,EACd,wBAAwB,EACxB,MAAM,EACN,cAAc,EACd,oBAAoB,EACpB,uBAAuB,EACvB,2BAA2B,EAC3B,mBAAmB,EACnB,6BAA6B,EAC7B,qCAAqC,EACrC,oCAAoC,EACpC,uBAAuB,EACvB,cAAc,EACd,gBAAgB,EACjB,MAAM,UAAU,CAAC;AAElB;;;;GAIG;AACH,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAEvC;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnD;;OAEG;IACH,KAAK,CAAC,EACF,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,CAAC;IAE5B;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;CACrC;AAED;;;;GAIG;AACH,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAEvC;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnD;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;CACrC;AAED;;;;GAIG;AACH,MAAM,WAAW,WAAW;IAC1B;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAEvC;;OAEG;IACH,KAAK,CAAC,EACF,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,CAAC;IAE5B;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;CACrC;AAED;;;;GAIG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAEvC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;CACrC;AAED;;;;GAIG;AACH,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;CACxC;AAED;;;;GAIG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;CACxC;AAED;;;;GAIG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;;OAGG;IACH,OAAO,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAEjC;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;;;GAIG;AACH,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;IAEvC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC;CACrC;AAED;;;;GAIG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;;;;;;;;;;;;OAcG;IACH,UAAU,CAAC,YAAY,EAAE,MAAM,GAAG,mBAAmB,CAAC;IAEtD;;;;;;;;;;;;OAYG;IACH,YAAY,CAAC,UAAU,SAAS,mBAAmB,GAAG,GAAG,EACvD,aAAa,EAAE,MAAM,EACrB,YAAY,CAAC,EAAE,UAAU,GACxB,2BAA2B,CAAC,UAAU,CAAC,CAAC;IAE3C;;;;;;;;;;;OAWG;IACH,QAAQ,CAAC,aAAa,SAAS,MAAM,EACnC,YAAY,EAAE,aAAa,EAC3B,IAAI,CAAC,EAAE,YAAY,EAAE,EACrB,OAAO,CAAC,EAAE,eAAe,GACxB,qCAAqC,CAAC,aAAa,CAAC,CAAC;IAExD;;;;;;;;;;;;OAYG;IACH,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,GAAG,EAC7B,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,EACxB,IAAI,CAAC,EAAE,YAAY,EAAE,EACrB,OAAO,CAAC,EAAE,eAAe,GACxB,wBAAwB,CAAC,IAAI,CAAC,CAAC;IAElC;;;;;;;;;;;OAWG;IACH,OAAO,CAAC,YAAY,SAAS,MAAM,EACjC,WAAW,EAAE,YAAY,EACzB,OAAO,CAAC,EAAE,cAAc,GACvB,oCAAoC,CAAC,YAAY,CAAC,CAAC;IAEtD;;;;;;;;;;;;;;;;OAgBG;IACH,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS,GAAG,EAC5B,WAAW,EAAE,MAAM,EACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,EACxB,OAAO,CAAC,EAAE,cAAc,GACvB,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEjC;;;;;;;;;;;;;OAaG;IACH,IAAI,CAAC,aAAa,SAAS,MAAM,EAAE,aAAa,SAAS,MAAM,EAC7D,cAAc,EAAE,sBAAsB,CAAC,aAAa,CAAC,EACrD,YAAY,EAAE,aAAa,EAC3B,IAAI,CAAC,EAAE,YAAY,EAAE,EACrB,OAAO,CAAC,EAAE,WAAW,GACpB,kBAAkB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAEpD;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,UAAU,CAAC,aAAa,SAAS,MAAM,EAAE,aAAa,SAAS,MAAM,EACnE,cAAc,EAAE,sBAAsB,CAAC,aAAa,CAAC,EACrD,YAAY,EAAE,aAAa,EAC3B,IAAI,CAAC,EAAE,YAAY,EAAE,EACrB,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,EAC7B,OAAO,CAAC,EAAE,iBAAiB,GAC1B,gBAAgB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAElD;;;;;;;;;;;;;;;;;OAiBG;IACH,kBAAkB,CAChB,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM,EAE5B,cAAc,EAAE,sBAAsB,CAAC,aAAa,CAAC,EACrD,YAAY,EAAE,aAAa,EAC3B,IAAI,CAAC,EAAE,YAAY,EAAE,EACrB,OAAO,CAAC,EAAE,yBAAyB,GAClC,wBAAwB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAE1D;;;;;;;;;;;;;;;OAeG;IACH,UAAU,CAAC,aAAa,SAAS,MAAM,EACrC,YAAY,EAAE,aAAa,EAC3B,OAAO,EACH,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,EACvC,OAAO,CAAC,EAAE,iBAAiB,GAC1B,6BAA6B,CAAC,aAAa,CAAC,CAAC;IAEhD;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,GAAG,EAC/B,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,EACxB,OAAO,EACH,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,EACvC,OAAO,CAAC,EAAE,iBAAiB,GAC1B,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAE1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,iBAAiB,CACf,gBAAgB,EACZ,qCAAqC,CAAC,MAAM,CAAC,GAC7C,wBAAwB,GACxB,cAAc,GACd,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,EACtC,SAAS,EAAE,MAAM,EACjB,WAAW,EAAE,MAAM,GAAG,MAAM,EAC5B,OAAO,CAAC,EAAE,wBAAwB,GACjC,uBAAuB,CAAC;IAE3B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,IAAI,CACF,EAAE,EAAE,MAAM,EACV,EAAE,EACE,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,GACnC,mBAAmB,EACvB,KAAK,CAAC,EAAE,MAAM,GAAG,2BAA2B,CAAC,MAAM,CAAC,EACpD,IAAI,CAAC,EAAE,MAAM,GAAG,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,EACxD,OAAO,CAAC,EAAE,eAAe,GACxB,cAAc,CAAC;IAElB;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,SAAS,CACP,SAAS,SAAS,MAAM,EACxB,aAAa,SAAS,MAAM,EAC5B,sBAAsB,SAAS,oBAAoB,CAAC,aAAa,CAAC,EAElE,iBAAiB,EAAE,cAAc,CAC/B,SAAS,EACT,aAAa,EACb,sBAAsB,CACvB,GACA,sBAAsB,CAAC;CAC3B"}