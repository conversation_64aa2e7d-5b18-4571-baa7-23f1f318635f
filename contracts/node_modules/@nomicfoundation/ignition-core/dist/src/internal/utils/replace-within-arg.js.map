{"version": 3, "file": "replace-within-arg.js", "sourceRoot": "", "sources": ["../../../../src/internal/utils/replace-within-arg.ts"], "names": [], "mappings": ";;;AAAA,mDAA6D;AAC7D,+CAO4B;AAgB5B;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,SAAgB,gBAAgB,CAC9B,GAAiB,EACjB,SAAuB;IAEvB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,OAAO,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC9B;IAED,IAAI,IAAA,sBAAQ,EAAC,GAAG,CAAC,EAAE;QACjB,OAAO,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAC9B;IAED,IAAI,IAAA,4BAAc,EAAC,GAAG,CAAC,EAAE;QACvB,IAAI,GAAG,CAAC,IAAI,KAAK,yBAAgB,CAAC,OAAO,EAAE;YACzC,OAAO,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;SAC3C;QAED,OAAO,SAAS,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC;KACnD;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;KACvD;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;QAC3C,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CACzE,CAAC;KACH;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AA/BD,4CA+BC"}