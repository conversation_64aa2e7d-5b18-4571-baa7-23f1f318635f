{"version": 3, "file": "module.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/module.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EACL,mBAAmB,EACnB,uBAAuB,EACvB,YAAY,EACZ,gBAAgB,EAChB,wBAAwB,EACxB,uBAAuB,EACvB,cAAc,EACd,MAAM,EACN,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,2BAA2B,EAC3B,mBAAmB,EACnB,6BAA6B,EAC7B,kBAAkB,EAClB,qCAAqC,EACrC,oCAAoC,EACpC,gBAAgB,EAChB,uBAAuB,EACvB,gBAAgB,EAChB,cAAc,EACd,wBAAwB,EACzB,MAAM,iBAAiB,CAAC;AAEzB,QAAA,MAAM,mBAAmB,eAAoC,CAAC;AAE9D,uBAAe,wBAAwB,CAAC,WAAW,SAAS,UAAU;aAIlD,EAAE,EAAE,MAAM;aACV,IAAI,EAAE,WAAW;aACjB,MAAM,EAAE,4BAA4B;IALtD,SAAgB,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,CAAa;gBAGrD,EAAE,EAAE,MAAM,EACV,IAAI,EAAE,WAAW,EACjB,MAAM,EAAE,4BAA4B;IAG/C,CAAC,mBAAmB,CAAC,CAC1B,MAAM,EAAE,MAAM,EACd,EAAE,OAAO,EAAE,EAAE;QAAE,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,MAAM,CAAA;KAAE;CAYhD;AAED,qBAAa,2CAA2C,CACpD,aAAa,SAAS,MAAM,CAE9B,SAAQ,wBAAwB,CAAC,UAAU,CAAC,kCAAkC,CAC9E,YAAW,qCAAqC,CAAC,aAAa,CAAC;aAG7C,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,aAAa;aAC3B,eAAe,EAAE,YAAY,EAAE;aAC/B,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;aACjD,KAAK,EACjB,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB;aACX,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;gBAV9C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,aAAa,EAC3B,eAAe,EAAE,YAAY,EAAE,EAC/B,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EACjD,KAAK,EACjB,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,EACX,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;CAIjE;AAED,qBAAa,8CAA8C,CACvD,aAAa,SAAS,MAAM,CAE9B,SAAQ,wBAAwB,CAAC,UAAU,CAAC,mBAAmB,CAC/D,YAAW,wBAAwB;aAGjB,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,aAAa;aAC3B,eAAe,EAAE,YAAY,EAAE;aAC/B,QAAQ,EAAE,QAAQ;aAClB,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;aACjD,KAAK,EACjB,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB;aACX,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;gBAX9C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,aAAa,EAC3B,eAAe,EAAE,YAAY,EAAE,EAC/B,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EACjD,KAAK,EACjB,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,EACX,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;CAIjE;AAED,qBAAa,0CAA0C,CACnD,YAAY,SAAS,MAAM,CAE7B,SAAQ,wBAAwB,CAAC,UAAU,CAAC,iCAAiC,CAC7E,YAAW,oCAAoC,CAAC,YAAY,CAAC;aAG3C,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,YAAY;aAC1B,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;aACjD,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;gBAJ9C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,YAAY,EAC1B,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EACjD,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;CAIjE;AAED,qBAAa,6CAA6C,CACtD,YAAY,SAAS,MAAM,CAE7B,SAAQ,wBAAwB,CAAC,UAAU,CAAC,kBAAkB,CAC9D,YAAW,uBAAuB;aAGhB,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,YAAY;aAC1B,QAAQ,EAAE,QAAQ;aAClB,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;aACjD,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;gBAL9C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,YAAY,EAC1B,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EACjD,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;CAIjE;AAED,qBAAa,qCAAqC,CAC9C,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM,CAE9B,SAAQ,wBAAwB,CAAC,UAAU,CAAC,aAAa,CACzD,YAAW,kBAAkB,CAAC,aAAa,EAAE,aAAa,CAAC;aAGzC,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,aAAa;aAC3B,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC;aACvC,IAAI,EAAE,YAAY,EAAE;aACpB,KAAK,EACjB,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB;aACX,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;gBAV9C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,aAAa,EAC3B,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC,EACvC,IAAI,EAAE,YAAY,EAAE,EACpB,KAAK,EACjB,MAAM,GACN,2BAA2B,CAAC,MAAM,CAAC,GACnC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,GAChC,uBAAuB,EACX,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;CAIjE;AAED,qBAAa,mCAAmC,CAC5C,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM,CAE9B,SAAQ,wBAAwB,CAAC,UAAU,CAAC,WAAW,CACvD,YAAW,gBAAgB,CAAC,aAAa,EAAE,aAAa,CAAC;aAGvC,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,aAAa;aAC3B,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC;aACvC,IAAI,EAAE,YAAY,EAAE;aACpB,WAAW,EAAE,MAAM,GAAG,MAAM;aAC5B,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;gBAN9C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,aAAa,EAC3B,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC,EACvC,IAAI,EAAE,YAAY,EAAE,EACpB,WAAW,EAAE,MAAM,GAAG,MAAM,EAC5B,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;CAIjE;AAED,qBAAa,2CAA2C,CACpD,aAAa,SAAS,MAAM,EAC5B,aAAa,SAAS,MAAM,CAE9B,SAAQ,wBAAwB,CAAC,UAAU,CAAC,oBAAoB,CAChE,YAAW,wBAAwB,CAAC,aAAa,EAAE,aAAa,CAAC;aAG/C,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,aAAa;aAC3B,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC;aACvC,IAAI,EAAE,YAAY,EAAE;gBAJpB,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,aAAa,EAC3B,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC,EACvC,IAAI,EAAE,YAAY,EAAE;CAIvC;AAED,qBAAa,mCAAmC,CAAC,aAAa,SAAS,MAAM,CAC3E,SAAQ,wBAAwB,CAAC,UAAU,CAAC,0BAA0B,CACtE,YAAW,6BAA6B,CAAC,aAAa,CAAC;aAGrC,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,aAAa;aAC3B,OAAO,EACnB,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC;gBANvB,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,aAAa,EAC3B,OAAO,EACnB,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC;CAI1C;AAED,qBAAa,sCACX,SAAQ,wBAAwB,CAAC,UAAU,CAAC,WAAW,CACvD,YAAW,gBAAgB;aAGT,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,YAAY,EAAE,MAAM;aACpB,OAAO,EACnB,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC;aACvB,QAAQ,EAAE,QAAQ;gBAPlB,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,YAAY,EAAE,MAAM,EACpB,OAAO,EACnB,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,EACvB,QAAQ,EAAE,QAAQ;CAIrC;AAED,qBAAa,qCACX,SAAQ,wBAAwB,CAAC,UAAU,CAAC,mBAAmB,CAC/D,YAAW,uBAAuB;aAGhB,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,gBAAgB,EAC5B,qCAAqC,CAAC,MAAM,CAAC,GAC7C,wBAAwB,GACxB,cAAc,GACd,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;aACtB,SAAS,EAAE,MAAM;aACjB,WAAW,EAAE,MAAM,GAAG,MAAM;aAC5B,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC;aAC/B,UAAU,EAAE,MAAM;gBAVlB,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,gBAAgB,EAC5B,qCAAqC,CAAC,MAAM,CAAC,GAC7C,wBAAwB,GACxB,cAAc,GACd,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,EACtB,SAAS,EAAE,MAAM,EACjB,WAAW,EAAE,MAAM,GAAG,MAAM,EAC5B,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,EAC/B,UAAU,EAAE,MAAM;CAIrC;AAED,qBAAa,4BACX,SAAQ,wBAAwB,CAAC,UAAU,CAAC,SAAS,CACrD,YAAW,cAAc;aAGP,EAAE,EAAE,MAAM;aACV,MAAM,EAAE,4BAA4B;aACpC,EAAE,EACd,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,GACnC,mBAAmB;aACP,KAAK,EAAE,MAAM,GAAG,2BAA2B,CAAC,MAAM,CAAC;aACnD,IAAI,EAChB,MAAM,GACN,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,GACxC,SAAS;aACG,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;gBAZ9C,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,4BAA4B,EACpC,EAAE,EACd,MAAM,GACN,uBAAuB,GACvB,2BAA2B,CAAC,MAAM,CAAC,GACnC,mBAAmB,EACP,KAAK,EAAE,MAAM,GAAG,2BAA2B,CAAC,MAAM,CAAC,EACnD,IAAI,EAChB,MAAM,GACN,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,GACxC,SAAS,EACG,IAAI,EAAE,MAAM,GAAG,mBAAmB,GAAG,SAAS;CAIjE;AAED,qBAAa,4BAA4B,CACvC,SAAS,SAAS,MAAM,GAAG,MAAM,EACjC,aAAa,SAAS,MAAM,GAAG,MAAM,EACrC,sBAAsB,SAAS,oBAAoB,CAAC,aAAa,CAAC,GAAG,oBAAoB,CAAC,aAAa,CAAC,CACxG,YAAW,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,sBAAsB,CAAC;aAMzD,EAAE,EAAE,SAAS;aACb,OAAO,EAAE,sBAAsB;IALjD,SAAgB,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,CAAa;IACjD,SAAgB,UAAU,EAAE,GAAG,CAAC,cAAc,CAAC,CAAa;gBAG1C,EAAE,EAAE,SAAS,EACb,OAAO,EAAE,sBAAsB;IAG1C,CAAC,mBAAmB,CAAC,CAC1B,MAAM,EAAE,MAAM,EACd,EAAE,OAAO,EAAE,EAAE;QAAE,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,MAAM,CAAA;KAAE;CAahD;AAED,qBAAa,iCAAkC,YAAW,mBAAmB;aAG/C,YAAY,EAAE,MAAM;IAFhD,SAAgB,IAAI,4BAA4B;gBAEpB,YAAY,EAAE,MAAM;IAEzC,CAAC,mBAAmB,CAAC,CAC1B,MAAM,EAAE,MAAM,EACd,eAAe,EAAE;QAAE,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,MAAM,CAAA;KAAE;CAMpD;AAED,qBAAa,yCAAyC,CACpD,UAAU,SAAS,mBAAmB,CACtC,YAAW,2BAA2B,CAAC,UAAU,CAAC;aAKhC,QAAQ,EAAE,MAAM;aAChB,IAAI,EAAE,MAAM;aACZ,YAAY,EAAE,UAAU,GAAG,SAAS;IALtD,SAAgB,IAAI,qCAAqC;gBAGvC,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,YAAY,EAAE,UAAU,GAAG,SAAS;IAG/C,CAAC,mBAAmB,CAAC,CAC1B,MAAM,EAAE,MAAM,EACd,EAAE,OAAO,EAAE,EAAE;QAAE,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,MAAM,CAAA;KAAE;CAWhD"}