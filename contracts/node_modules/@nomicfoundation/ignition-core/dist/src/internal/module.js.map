{"version": 3, "file": "module.js", "sourceRoot": "", "sources": ["../../../src/internal/module.ts"], "names": [], "mappings": ";;;AACA,4CAuByB;AAEzB,MAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAE9D,MAAe,wBAAwB;IAInB;IACA;IACA;IALF,YAAY,GAAiC,IAAI,GAAG,EAAE,CAAC;IAEvE,YACkB,EAAU,EACV,IAAiB,EACjB,MAAoC;QAFpC,OAAE,GAAF,EAAE,CAAQ;QACV,SAAI,GAAJ,IAAI,CAAa;QACjB,WAAM,GAAN,MAAM,CAA8B;IACnD,CAAC;IAEG,CAAC,mBAAmB,CAAC,CAC1B,MAAc,EACd,EAAE,OAAO,EAAoC;QAE7C,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE9B,OAAO,UAAU,IAAI,CAAC,EAAE;YAChB,mBAAU,CAAC,IAAI,CAAC,IAAI,CAAC;cACnB,IAAI,CAAC,MAAM,CAAC,EAAE;oBACR,OAAO,CACrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAC/C,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE,CAAC;IAChC,CAAC;IACH,CAAC;CACF;AAED,MAAa,2CAGX,SAAQ,wBAAuE;IAI7D;IACA;IACA;IACA;IACA;IACA;IAKA;IAXlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA2B,EAC3B,eAA+B,EAC/B,SAAiD,EACjD,KAIW,EACX,IAA8C;QAE9D,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC;QAZjD,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAe;QAC3B,oBAAe,GAAf,eAAe,CAAgB;QAC/B,cAAS,GAAT,SAAS,CAAwC;QACjD,UAAK,GAAL,KAAK,CAIM;QACX,SAAI,GAAJ,IAAI,CAA0C;IAGhE,CAAC;CACF;AArBD,kGAqBC;AAED,MAAa,8CAGX,SAAQ,wBAAwD;IAI9C;IACA;IACA;IACA;IACA;IACA;IACA;IAKA;IAZlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA2B,EAC3B,eAA+B,EAC/B,QAAkB,EAClB,SAAiD,EACjD,KAIW,EACX,IAA8C;QAE9D,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAblC,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAe;QAC3B,oBAAe,GAAf,eAAe,CAAgB;QAC/B,aAAQ,GAAR,QAAQ,CAAU;QAClB,cAAS,GAAT,SAAS,CAAwC;QACjD,UAAK,GAAL,KAAK,CAIM;QACX,SAAI,GAAJ,IAAI,CAA0C;IAGhE,CAAC;CACF;AAtBD,wGAsBC;AAED,MAAa,0CAGX,SAAQ,wBAAsE;IAI5D;IACA;IACA;IACA;IACA;IALlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA0B,EAC1B,SAAiD,EACjD,IAA8C;QAE9D,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;QANhD,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,cAAS,GAAT,SAAS,CAAwC;QACjD,SAAI,GAAJ,IAAI,CAA0C;IAGhE,CAAC;CACF;AAfD,gGAeC;AAED,MAAa,6CAGX,SAAQ,wBAAuD;IAI7C;IACA;IACA;IACA;IACA;IACA;IANlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA0B,EAC1B,QAAkB,EAClB,SAAiD,EACjD,IAA8C;QAE9D,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAPjC,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,aAAQ,GAAR,QAAQ,CAAU;QAClB,cAAS,GAAT,SAAS,CAAwC;QACjD,SAAI,GAAJ,IAAI,CAA0C;IAGhE,CAAC;CACF;AAhBD,sGAgBC;AAED,MAAa,qCAIX,SAAQ,wBAAkD;IAIxC;IACA;IACA;IACA;IACA;IACA;IAKA;IAXlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA2B,EAC3B,QAAuC,EACvC,IAAoB,EACpB,KAIW,EACX,IAA8C;QAE9D,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAZ5B,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAe;QAC3B,aAAQ,GAAR,QAAQ,CAA+B;QACvC,SAAI,GAAJ,IAAI,CAAgB;QACpB,UAAK,GAAL,KAAK,CAIM;QACX,SAAI,GAAJ,IAAI,CAA0C;IAGhE,CAAC;CACF;AAtBD,sFAsBC;AAED,MAAa,mCAIX,SAAQ,wBAAgD;IAItC;IACA;IACA;IACA;IACA;IACA;IACA;IAPlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA2B,EAC3B,QAAuC,EACvC,IAAoB,EACpB,WAA4B,EAC5B,IAA8C;QAE9D,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAR1B,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAe;QAC3B,aAAQ,GAAR,QAAQ,CAA+B;QACvC,SAAI,GAAJ,IAAI,CAAgB;QACpB,gBAAW,GAAX,WAAW,CAAiB;QAC5B,SAAI,GAAJ,IAAI,CAA0C;IAGhE,CAAC;CACF;AAlBD,kFAkBC;AAED,MAAa,2CAIX,SAAQ,wBAAyD;IAI/C;IACA;IACA;IACA;IACA;IALlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA2B,EAC3B,QAAuC,EACvC,IAAoB;QAEpC,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;QANnC,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAe;QAC3B,aAAQ,GAAR,QAAQ,CAA+B;QACvC,SAAI,GAAJ,IAAI,CAAgB;IAGtC,CAAC;CACF;AAhBD,kGAgBC;AAED,MAAa,mCACX,SAAQ,wBAA+D;IAIrD;IACA;IACA;IACA;IAJlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAA2B,EAC3B,OAGuB;QAEvC,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;QARzC,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAe;QAC3B,YAAO,GAAP,OAAO,CAGgB;IAGzC,CAAC;CACF;AAfD,kFAeC;AAED,MAAa,sCACX,SAAQ,wBAAgD;IAItC;IACA;IACA;IACA;IAIA;IARlB,YACkB,EAAU,EACV,MAAoC,EACpC,YAAoB,EACpB,OAGuB,EACvB,QAAkB;QAElC,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAT1B,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,iBAAY,GAAZ,YAAY,CAAQ;QACpB,YAAO,GAAP,OAAO,CAGgB;QACvB,aAAQ,GAAR,QAAQ,CAAU;IAGpC,CAAC;CACF;AAhBD,wFAgBC;AAED,MAAa,qCACX,SAAQ,wBAAwD;IAI9C;IACA;IACA;IAKA;IACA;IACA;IACA;IAXlB,YACkB,EAAU,EACV,MAAoC,EACpC,gBAIsB,EACtB,SAAiB,EACjB,WAA4B,EAC5B,OAA+B,EAC/B,UAAkB;QAElC,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAZlC,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,qBAAgB,GAAhB,gBAAgB,CAIM;QACtB,cAAS,GAAT,SAAS,CAAQ;QACjB,gBAAW,GAAX,WAAW,CAAiB;QAC5B,YAAO,GAAP,OAAO,CAAwB;QAC/B,eAAU,GAAV,UAAU,CAAQ;IAGpC,CAAC;CACF;AAnBD,sFAmBC;AAED,MAAa,4BACX,SAAQ,wBAA8C;IAIpC;IACA;IACA;IAKA;IACA;IAIA;IAblB,YACkB,EAAU,EACV,MAAoC,EACpC,EAIO,EACP,KAAmD,EACnD,IAGH,EACG,IAA8C;QAE9D,KAAK,CAAC,EAAE,EAAE,mBAAU,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAdxB,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAA8B;QACpC,OAAE,GAAF,EAAE,CAIK;QACP,UAAK,GAAL,KAAK,CAA8C;QACnD,SAAI,GAAJ,IAAI,CAGP;QACG,SAAI,GAAJ,IAAI,CAA0C;IAGhE,CAAC;CACF;AArBD,oEAqBC;AAED,MAAa,4BAA4B;IAUrB;IACA;IALF,OAAO,GAAgB,IAAI,GAAG,EAAE,CAAC;IACjC,UAAU,GAAwB,IAAI,GAAG,EAAE,CAAC;IAE5D,YACkB,EAAa,EACb,OAA+B;QAD/B,OAAE,GAAF,EAAE,CAAW;QACb,YAAO,GAAP,OAAO,CAAwB;IAC9C,CAAC;IAEG,CAAC,mBAAmB,CAAC,CAC1B,MAAc,EACd,EAAE,OAAO,EAAoC;QAE7C,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE9B,OAAO,kBAAkB,IAAI,CAAC,EAAE;eACrB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE,CAAC;eACpD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE,CAAC;kBACjD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CACzE,KAAK,EACL,KAAK,OAAO,EAAE,CACf;IACD,CAAC;IACH,CAAC;CACF;AA7BD,oEA6BC;AAED,MAAa,iCAAiC;IAGhB;IAFZ,IAAI,GAAG,yBAAgB,CAAC,OAAO,CAAC;IAEhD,YAA4B,YAAoB;QAApB,iBAAY,GAAZ,YAAY,CAAQ;IAAG,CAAC;IAE7C,CAAC,mBAAmB,CAAC,CAC1B,MAAc,EACd,eAAiD;QAEjD,OAAO;oBACS,IAAI,CAAC,YAAY;EACnC,CAAC;IACD,CAAC;CACF;AAbD,8EAaC;AAED,MAAa,yCAAyC;IAOlC;IACA;IACA;IALF,IAAI,GAAG,yBAAgB,CAAC,gBAAgB,CAAC;IAEzD,YACkB,QAAgB,EAChB,IAAY,EACZ,YAAoC;QAFpC,aAAQ,GAAR,QAAQ,CAAQ;QAChB,SAAI,GAAJ,IAAI,CAAQ;QACZ,iBAAY,GAAZ,YAAY,CAAwB;IACnD,CAAC;IAEG,CAAC,mBAAmB,CAAC,CAC1B,MAAc,EACd,EAAE,OAAO,EAAoC;QAE7C,OAAO;YACC,IAAI,CAAC,IAAI,GACf,IAAI,CAAC,YAAY,KAAK,SAAS;YAC7B,CAAC,CAAC;qBACW,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YACzC,CAAC,CAAC,EACN;EACF,CAAC;IACD,CAAC;CACF;AAzBD,8FAyBC"}