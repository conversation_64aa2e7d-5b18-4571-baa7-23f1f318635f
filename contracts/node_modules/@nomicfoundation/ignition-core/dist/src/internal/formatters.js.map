{"version": 3, "file": "formatters.js", "sourceRoot": "", "sources": ["../../../src/internal/formatters.ts"], "names": [], "mappings": ";;;AAEA,mEAIyC;AACzC,yEAO4C;AAC5C,+GAAuG;AAEvG;;GAEG;AACH,SAAgB,oBAAoB,CAClC,MAKgC;IAEhC,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,sCAAmB,CAAC,gBAAgB;YACvC,OAAO,iDAAiD,8BAA8B,CACpF,MAAM,CAAC,KAAK,CACb,EAAE,CAAC;QACN,KAAK,sCAAmB,CAAC,yBAAyB;YAChD,OAAO,iDAAiD,MAAM,CAAC,KAAK,EAAE,CAAC;QACzE,KAAK,sCAAmB,CAAC,oBAAoB;YAC3C,OAAO,eAAe,MAAM,CAAC,MAAM,WAAW,CAAC;QACjD,KAAK,sCAAmB,CAAC,iBAAiB;YACxC,OAAO,kCAAkC,8BAA8B,CACrE,MAAM,CAAC,KAAK,CACb,EAAE,CAAC;QACN,KAAK,sCAAmB,CAAC,cAAc;YACrC,OAAO,gCAAgC,MAAM,CAAC,KAAK,EAAE,CAAC;KACzD;AACH,CAAC;AAxBD,oDAwBC;AAED;;GAEG;AACH,SAAgB,8BAA8B,CAC5C,MAAgC;IAEhC,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,uCAAuB,CAAC,oBAAoB;YAC/C,OAAO,uBAAuB,CAAC;QAEjC,KAAK,uCAAuB,CAAC,qBAAqB;YAChD,OAAO,yBAAyB,CAAC;QAEnC,KAAK,uCAAuB,CAAC,kBAAkB;YAC7C,OAAO,yBAAyB,MAAM,CAAC,OAAO,GAAG,CAAC;QAEpD,KAAK,uCAAuB,CAAC,sBAAsB;YACjD,OAAO,4BAA4B,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,IAAI,CAAC;QAE/E,KAAK,uCAAuB,CAAC,wBAAwB;YACnD,OAAO,8BAA8B,iBAAiB,CACpD,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,IAAI,CACZ,EAAE,CAAC;QAEN,KAAK,uCAAuB,CAAC,gCAAgC;YAC3D,OAAO,iDAAiD,MAAM,CAAC,SAAS,GAAG,CAAC;QAE9E,KAAK,uCAAuB,CAAC,wBAAwB;YACnD,OAAO,mCAAmC,CAAC;QAE7C,KAAK,uCAAuB,CAAC,gDAAgD;YAC3E,OAAO,wEAAwE,MAAM,CAAC,SAAS,GAAG,CAAC;KACtG;AACH,CAAC;AA/BD,wEA+BC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,SAAiB,EAAE,IAAc;IACjE,MAAM,eAAe,GAAG,IAAA,oEAA8B,EAAC,IAAI,CAAC,CAAC;IAC7D,OAAO,GAAG,SAAS,IAAI,eAAe;SACnC,GAAG,CAAC,uBAAuB,CAAC;SAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AACnB,CAAC;AALD,8CAKC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CAAC,KAA4B;IAClE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACxB,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAElD,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;KACjC;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CACtC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,MAAM,uBAAuB,CAAC,KAAK,CAAC,EAAE,CAChE,CAAC;QAEF,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;KACjC;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,IAAI,KAAK,GAAG,CAAC;KACrB;IAED,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC1B,CAAC;AApBD,0DAoBC"}