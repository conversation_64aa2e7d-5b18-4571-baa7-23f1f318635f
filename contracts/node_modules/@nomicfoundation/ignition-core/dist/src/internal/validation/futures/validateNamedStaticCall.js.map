{"version": 3, "file": "validateNamedStaticCall.js", "sourceRoot": "", "sources": ["../../../../../src/internal/validation/futures/validateNamedStaticCall.ts"], "names": [], "mappings": ";;;AAAA,4CAAgD;AAChD,sDAI8B;AAI9B,mDAA2C;AAC3C,6CAG6B;AAC7B,oCAKkB;AAEX,KAAK,UAAU,uBAAuB,CAC3C,MAAwC,EACxC,cAAgC,EAChC,oBAA0C,EAC1C,QAAkB;IAElB,MAAM,MAAM,GAAoB,EAAE,CAAC;IAEnC,eAAe;IAEf,MAAM,QAAQ,GACZ,UAAU,IAAI,MAAM,CAAC,QAAQ;QAC3B,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ;QAC1B,CAAC,CAAC,MAAM,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAEtE,IAAI,CAAC,IAAA,4BAAc,EAAC,QAAQ,CAAC,EAAE;QAC7B,MAAM,CAAC,IAAI,CACT,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE;YACpD,YAAY,EAAE,MAAM,CAAC,QAAQ,CAAC,YAAY;SAC3C,CAAC,CACH,CAAC;KACH;SAAM;QACL,MAAM,CAAC,IAAI,CACT,GAAG,IAAA,8BAAwB,EACzB,QAAQ,EACR,MAAM,CAAC,QAAQ,CAAC,YAAY,EAC5B,MAAM,CAAC,YAAY,EACnB,MAAM,CAAC,IAAI,EACX,IAAI,CACL,CACF,CAAC;QAEF,MAAM,CAAC,IAAI,CACT,GAAG,IAAA,uCAAiC,EAClC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAC5B,MAAM,CAAC,YAAY,EACnB,QAAQ,EACR,MAAM,CAAC,WAAW,CACnB,CACF,CAAC;KACH;IAED,eAAe;IAEf,MAAM,aAAa,GAAG,IAAA,mCAA2B,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,2CAA6B,CAAC,CAAC;IACzE,MAAM,aAAa,GAAG;QACpB,GAAG,IAAA,oCAA4B,EAAC,aAAa,CAAC;QAC9C,GAAG,CAAC,IAAA,mCAAqB,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KAC7D,CAAC;IAEF,MAAM,CAAC,IAAI,CACT,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAC/B,IAAA,mCAA2B,EAAC,GAAG,EAAE,QAAQ,CAAC,CAC3C,CACF,CAAC;IAEF,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CACvC,CAAC,KAAK,EAAE,EAAE,CACR,IAAA,gDAAwC,EAAC,oBAAoB,EAAE,KAAK,CAAC;QACrE,SAAS,CACZ,CAAC;IAEF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM,CAAC,IAAI,CACT,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,wBAAwB,EAAE;YAC5D,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI;SAC5B,CAAC,CACH,CAAC;KACH;IAED,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAxED,0DAwEC"}