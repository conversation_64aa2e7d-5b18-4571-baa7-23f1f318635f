import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { NamedArtifactContractAtFuture } from "../../../types/module";
export declare function validateNamedContractAt(future: NamedArtifactContractAtFuture<string>, artifactLoader: ArtifactResolver, deploymentParameters: DeploymentParameters, _accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateNamedContractAt.d.ts.map