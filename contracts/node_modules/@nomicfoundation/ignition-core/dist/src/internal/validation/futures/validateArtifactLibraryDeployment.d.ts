import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { LibraryDeploymentFuture } from "../../../types/module";
export declare function validateArtifactLibraryDeployment(future: LibraryDeploymentFuture, _artifactLoader: ArtifactResolver, _deploymentParameters: DeploymentParameters, accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateArtifactLibraryDeployment.d.ts.map