{"version": 3, "file": "validateSendData.js", "sourceRoot": "", "sources": ["../../../../../src/internal/validation/futures/validateSendData.ts"], "names": [], "mappings": ";;;AAAA,4CAAgD;AAChD,sDAG8B;AAI9B,mDAA2C;AAC3C,oCAGkB;AAEX,KAAK,UAAU,gBAAgB,CACpC,MAAsB,EACtB,eAAiC,EACjC,oBAA0C,EAC1C,QAAkB;IAElB,MAAM,MAAM,GAAoB,EAAE,CAAC;IAEnC,eAAe;IAEf,MAAM,aAAa,GAAG;QACpB,GAAG,CAAC,IAAA,mCAAqB,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,GAAG,CAAC,IAAA,mCAAqB,EAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACzD,CAAC;IAEF,MAAM,CAAC,IAAI,CACT,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAC/B,IAAA,mCAA2B,EAAC,GAAG,EAAE,QAAQ,CAAC,CAC3C,CACF,CAAC;IAEF,IAAI,IAAA,2CAA6B,EAAC,MAAM,CAAC,EAAE,CAAC,EAAE;QAC5C,MAAM,KAAK,GAAG,IAAA,gDAAwC,EACpD,oBAAoB,EACpB,MAAM,CAAC,EAAE,CACV,CAAC;QAEF,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,CAAC,IAAI,CACT,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,wBAAwB,EAAE;gBAC5D,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI;aACrB,CAAC,CACH,CAAC;SACH;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,MAAM,CAAC,IAAI,CACT,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,6BAA6B,EAAE;gBACjE,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI;gBACpB,YAAY,EAAE,QAAQ;gBACtB,UAAU,EAAE,OAAO,KAAK;aACzB,CAAC,CACH,CAAC;SACH;KACF;IAED,IAAI,IAAA,2CAA6B,EAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QAC/C,MAAM,KAAK,GAAG,IAAA,gDAAwC,EACpD,oBAAoB,EACpB,MAAM,CAAC,KAAK,CACb,CAAC;QAEF,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,CAAC,IAAI,CACT,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,wBAAwB,EAAE;gBAC5D,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;aACxB,CAAC,CACH,CAAC;SACH;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,MAAM,CAAC,IAAI,CACT,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,6BAA6B,EAAE;gBACjE,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;gBACvB,YAAY,EAAE,QAAQ;gBACtB,UAAU,EAAE,OAAO,KAAK;aACzB,CAAC,CACH,CAAC;SACH;KACF;IAED,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AApED,4CAoEC"}