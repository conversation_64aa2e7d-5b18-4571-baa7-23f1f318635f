import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { ContractAtFuture } from "../../../types/module";
export declare function validateArtifactContractAt(future: ContractAtFuture, _artifactLoader: ArtifactResolver, deploymentParameters: DeploymentParameters, _accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateArtifactContractAt.d.ts.map