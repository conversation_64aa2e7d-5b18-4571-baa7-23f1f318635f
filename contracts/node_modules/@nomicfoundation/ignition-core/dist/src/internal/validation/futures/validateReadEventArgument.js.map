{"version": 3, "file": "validateReadEventArgument.js", "sourceRoot": "", "sources": ["../../../../../src/internal/validation/futures/validateReadEventArgument.ts"], "names": [], "mappings": ";;;AAAA,4CAAgD;AAChD,sDAAsD;AAItD,mDAA2C;AAC3C,6CAA0E;AAEnE,KAAK,UAAU,yBAAyB,CAC7C,MAA+B,EAC/B,cAAgC,EAChC,qBAA2C,EAC3C,SAAmB;IAEnB,MAAM,MAAM,GAAoB,EAAE,CAAC;IAEnC,eAAe;IAEf,MAAM,QAAQ,GACZ,UAAU,IAAI,MAAM,CAAC,OAAO;QAC1B,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ;QACzB,CAAC,CAAC,MAAM,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAErE,IAAI,CAAC,IAAA,4BAAc,EAAC,QAAQ,CAAC,EAAE;QAC7B,MAAM,CAAC,IAAI,CACT,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE;YACpD,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY;SAC1C,CAAC,CACH,CAAC;KACH;SAAM;QACL,MAAM,CAAC,IAAI,CACT,GAAG,IAAA,yCAAmC,EACpC,QAAQ,EACR,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,WAAW,CACnB,CACF,CAAC;KACH;IAED,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAhCD,8DAgCC"}