import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { NamedArtifactLibraryDeploymentFuture } from "../../../types/module";
export declare function validateNamedLibraryDeployment(future: NamedArtifactLibraryDeploymentFuture<string>, artifactLoader: ArtifactResolver, _deploymentParameters: DeploymentParameters, accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateNamedLibraryDeployment.d.ts.map