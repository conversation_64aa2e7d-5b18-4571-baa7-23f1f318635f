import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { ContractCallFuture } from "../../../types/module";
export declare function validateNamedContractCall(future: ContractCallFuture<string, string>, artifactLoader: ArtifactResolver, deploymentParameters: DeploymentParameters, accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateNamedContractCall.d.ts.map