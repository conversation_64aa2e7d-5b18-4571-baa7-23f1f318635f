import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { NamedArtifactContractDeploymentFuture } from "../../../types/module";
export declare function validateNamedContractDeployment(future: NamedArtifactContractDeploymentFuture<string>, artifactLoader: ArtifactResolver, deploymentParameters: DeploymentParameters, accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateNamedContractDeployment.d.ts.map