import { LibraryDeploymentFuture } from "../../../types/module";
import { DeploymentExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileArtifactLibraryDeployment(future: LibraryDeploymentFuture, executionState: DeploymentExecutionState, context: ReconciliationContext): Promise<ReconciliationFutureResult>;
//# sourceMappingURL=reconcileArtifactLibraryDeployment.d.ts.map