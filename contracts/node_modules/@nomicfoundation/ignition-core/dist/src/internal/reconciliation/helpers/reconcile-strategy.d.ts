import { ContractAtFuture, ContractCallFuture, ContractDeploymentFuture, EncodeFunctionCallFuture, LibraryDeploymentFuture, NamedArtifactContractAtFuture, NamedArtifactContractDeploymentFuture, NamedArtifactLibraryDeploymentFuture, ReadEventArgumentFuture, SendDataFuture, StaticCallFuture } from "../../../types/module";
import { CallExecutionState, ContractAtExecutionState, DeploymentExecutionState, EncodeFunctionCallExecutionState, ReadEventArgumentExecutionState, SendDataExecutionState, StaticCallExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResultFailure } from "../types";
export declare function reconcileStrategy(future: NamedArtifactContractDeploymentFuture<string> | ContractDeploymentFuture | NamedArtifactLibraryDeploymentFuture<string> | LibraryDeploymentFuture | NamedArtifactContractAtFuture<string> | ContractAtFuture | ContractCallFuture<string, string> | StaticCallFuture<string, string> | EncodeFunctionCallFuture<string, string> | SendDataFuture | ReadEventArgumentFuture, exState: DeploymentExecutionState | CallExecutionState | StaticCallExecutionState | EncodeFunctionCallExecutionState | ContractAtExecutionState | SendDataExecutionState | ReadEventArgumentExecutionState, context: ReconciliationContext): ReconciliationFutureResultFailure | undefined;
//# sourceMappingURL=reconcile-strategy.d.ts.map