{"version": 3, "file": "reconciler.js", "sourceRoot": "", "sources": ["../../../../src/internal/reconciliation/reconciler.ts"], "names": [], "mappings": ";;;AAAA,yCAA6C;AAK7C,gDAAwC;AAExC,wEAI4C;AAC5C,4DAAwD;AACxD,gFAA2E;AAC3E,8EAAwE;AAExE,2GAAmG;AACnG,6EAAwE;AACxE,2GAAqG;AASrG,MAAa,UAAU;IACd,MAAM,CAAC,KAAK,CAAC,SAAS,CAC3B,MAAsB,EACtB,eAAgC,EAChC,oBAA0C,EAC1C,QAAkB,EAClB,gBAAkC,EAClC,gBAAkC,EAClC,aAAqB,EACrB,QAAgB,EAChB,cAAuC;QAEvC,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,4BAA4B,CACpE,MAAM,EACN;YACE,eAAe;YACf,oBAAoB;YACpB,QAAQ;YACR,gBAAgB;YAChB,gBAAgB;YAChB,aAAa;YACb,QAAQ;YACR,cAAc;SACf,EACD;YACE,gFAAoC;YACpC,qDAAwB;YACxB,kFAAsC;SACvC,CACF,CAAC;QAEF,gDAAgD;QAEhD,MAAM,sBAAsB,GAAG,IAAI,CAAC,iCAAiC,CACnE,MAAM,EACN,eAAe,CAChB,CAAC;QAEF,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,CAAC;IAC5D,CAAC;IAEM,MAAM,CAAC,yBAAyB,CACrC,eAAgC;QAEhC,MAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,CACtC,eAAe,CAAC,eAAe,CAChC,CAAC,MAAM,CACN,CAAC,OAAO,EAAE,EAAE,CACV,OAAO,CAAC,MAAM,KAAK,iCAAe,CAAC,MAAM;YACzC,OAAO,CAAC,MAAM,KAAK,iCAAe,CAAC,OAAO,CAC7C,CAAC;QAEF,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC1C,QAAQ,EAAE,OAAO,CAAC,EAAE;YACpB,OAAO,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC;SACpD,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,4BAA4B,CAAC,OAAuB;QACjE,IAAI,OAAO,CAAC,MAAM,KAAK,iCAAe,CAAC,MAAM,EAAE;YAC7C,OAAO,kCAAkC,OAAO,CAAC,EAAE,mDAAmD,CAAC;SACxG;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,iCAAe,CAAC,OAAO,EAAE;YAC9C,OAAO,kCAAkC,OAAO,CAAC,EAAE,sDAAsD,CAAC;SAC3G;QAED,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,cAAc,CAAC,wBAAwB,EAAE;YACtE,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAC/C,MAAsB,EACtB,OAA8B,EAC9B,MAA6B;QAE7B,2DAA2D;QAC3D,iCAAiC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,oCAAoC,CAAC,MAAM,CAAC,CAAC;QAElE,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACnE,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,SAAS;aACV;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACnE,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,SAAS;aACV;YAED,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC/B;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,MAAM,CAAC,iCAAiC,CAC9C,MAAsB,EACtB,eAAgC;QAEhC,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,IAAA,8CAAoB,EAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAC9C,CAAC;QAEF,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CACrC,eAAe,CAAC,eAAe,CAChC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAErB,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzE,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,MAAM,CAAC,oCAAoC,CACjD,MAAsB;QAEtB,MAAM,OAAO,GAAG,IAAA,8CAAoB,EAAC,MAAM,CAAC,CAAC;QAE7C,MAAM,aAAa,GACjB,iDAAsB,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;QAEhE,MAAM,eAAe,GACnB,8BAAa,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,CAAC;QAEzD,OAAO,eAAe;aACnB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;aAC7C,MAAM,CAAC,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC;IACjD,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,MAAM,CACzB,MAAc,EACd,cAA8B,EAC9B,OAA8B,EAC9B,MAA6B;QAE7B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAE5D,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,SAAS;aACV;YAED,OAAO,MAAM,CAAC;SACf;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;CACF;AAvJD,gCAuJC"}