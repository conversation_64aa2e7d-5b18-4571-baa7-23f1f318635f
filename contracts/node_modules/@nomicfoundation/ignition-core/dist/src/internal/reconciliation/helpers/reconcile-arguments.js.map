{"version": 3, "file": "reconcile-arguments.js", "sourceRoot": "", "sources": ["../../../../../src/internal/reconciliation/helpers/reconcile-arguments.ts"], "names": [], "mappings": ";;;AAAA,sDAA0D;AAQ1D,gGAAwF;AACxF,2EAM+C;AAC/C,2DAA0E;AAK1E,oCAAgC;AAEhC,SAAgB,kBAAkB,CAChC,MAK4C,EAC5C,OAIoC,EACpC,OAA8B;IAE9B,MAAM,oBAAoB,GAAG,IAAA,gCAAkB,EAAC,MAAM,CAAC;QACrD,CAAC,CAAC,MAAM,CAAC,eAAe;QACxB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;IAEhB,MAAM,UAAU,GAAG,IAAA,8BAAW,EAC5B,oBAAoB,EACpB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,oBAAoB,EAC5B,OAAO,CAAC,QAAQ,CACjB,CAAC;IAEF,MAAM,WAAW,GACf,OAAO,CAAC,IAAI,KAAK,oCAAkB,CAAC,0BAA0B;QAC5D,CAAC,CAAC,OAAO,CAAC,eAAe;QACzB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IAEnB,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;QAC5C,OAAO,IAAA,YAAI,EACT,MAAM,EACN,wCAAwC,WAAW,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,EAAE,CACrF,CAAC;KACH;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAoC,CAAC;IAC7E,KAAK,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,EAAE;QACjD,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAElC,0EAA0E;QAC1E,2CAA2C;QAC3C,IAAI,IAAA,mBAAS,EAAC,SAAS,CAAC,IAAI,IAAA,mBAAS,EAAC,UAAU,CAAC,EAAE;YACjD,IAAI,CAAC,IAAA,wBAAc,EAAC,SAAS,EAAE,UAAU,CAAC,EAAE;gBAC1C,OAAO,IAAA,YAAI,EAAC,MAAM,EAAE,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;aAChE;SACF;aAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;YAC1C,OAAO,IAAA,YAAI,EAAC,MAAM,EAAE,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;SAChE;KACF;AACH,CAAC;AAnDD,gDAmDC"}