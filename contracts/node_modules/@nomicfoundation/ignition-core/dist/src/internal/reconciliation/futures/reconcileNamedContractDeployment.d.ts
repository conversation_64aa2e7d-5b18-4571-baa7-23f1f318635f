import { NamedArtifactContractDeploymentFuture } from "../../../types/module";
import { DeploymentExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileNamedContractDeployment(future: NamedArtifactContractDeploymentFuture<string>, executionState: DeploymentExecutionState, context: ReconciliationContext): Promise<ReconciliationFutureResult>;
//# sourceMappingURL=reconcileNamedContractDeployment.d.ts.map