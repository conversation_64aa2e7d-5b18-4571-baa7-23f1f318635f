import { ContractDeploymentFuture } from "../../../types/module";
import { DeploymentExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileArtifactContractDeployment(future: ContractDeploymentFuture, executionState: DeploymentExecutionState, context: ReconciliationContext): Promise<ReconciliationFutureResult>;
//# sourceMappingURL=reconcileArtifactContractDeployment.d.ts.map