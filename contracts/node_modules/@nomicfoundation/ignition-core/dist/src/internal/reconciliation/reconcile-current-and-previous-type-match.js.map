{"version": 3, "file": "reconcile-current-and-previous-type-match.js", "sourceRoot": "", "sources": ["../../../../src/internal/reconciliation/reconcile-current-and-previous-type-match.ts"], "names": [], "mappings": ";;;AAAA,+CAAwD;AAIxD,mCAA+B;AAE/B,SAAgB,oCAAoC,CAClD,MAAc,EACd,cAA8B,EAC9B,QAA+B;IAE/B,IAAI,cAAc,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,EAAE;QAC7C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;KAC1B;IAED,OAAO,IAAA,YAAI,EACT,MAAM,EACN,kBAAkB,MAAM,CAAC,EAAE,qBACzB,mBAAU,CAAC,cAAc,CAAC,UAAU,CACtC,OAAO,mBAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjC,CAAC;AACJ,CAAC;AAfD,oFAeC"}