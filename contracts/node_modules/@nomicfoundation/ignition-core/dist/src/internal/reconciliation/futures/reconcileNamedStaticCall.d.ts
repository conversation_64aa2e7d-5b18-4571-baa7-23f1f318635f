import { StaticCallFuture } from "../../../types/module";
import { StaticCallExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileNamedStaticCall(future: StaticCallFuture<string, string>, executionState: StaticCallExecutionState, context: ReconciliationContext): ReconciliationFutureResult;
//# sourceMappingURL=reconcileNamedStaticCall.d.ts.map