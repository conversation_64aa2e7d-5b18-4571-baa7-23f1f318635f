import { NamedArtifactLibraryDeploymentFuture } from "../../../types/module";
import { DeploymentExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileNamedLibraryDeployment(future: NamedArtifactLibraryDeploymentFuture<string>, executionState: DeploymentExecutionState, context: ReconciliationContext): Promise<ReconciliationFutureResult>;
//# sourceMappingURL=reconcileNamedLibraryDeployment.d.ts.map