import { ContractCallFuture } from "../../../types/module";
import { CallExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileNamedContractCall(future: ContractCallFuture<string, string>, executionState: CallExecutionState, context: ReconciliationContext): ReconciliationFutureResult;
//# sourceMappingURL=reconcileNamedContractCall.d.ts.map