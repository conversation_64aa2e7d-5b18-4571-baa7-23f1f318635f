import { NamedArtifactContractAtFuture } from "../../../types/module";
import { ContractAtExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileNamedContractAt(future: NamedArtifactContractAtFuture<string>, executionState: ContractAtExecutionState, context: ReconciliationContext): Promise<ReconciliationFutureResult>;
//# sourceMappingURL=reconcileNamedContractAt.d.ts.map