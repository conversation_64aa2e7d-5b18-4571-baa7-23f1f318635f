import { Future } from "../../types/module";
import { ExecutionState } from "../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "./types";
export declare function reconcileCurrentAndPreviousTypeMatch(future: Future, executionState: ExecutionState, _context: ReconciliationContext): ReconciliationFutureResult;
//# sourceMappingURL=reconcile-current-and-previous-type-match.d.ts.map