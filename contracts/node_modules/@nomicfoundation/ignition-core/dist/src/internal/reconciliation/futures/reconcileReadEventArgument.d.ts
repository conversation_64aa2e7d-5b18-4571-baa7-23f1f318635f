import { ReadEventArgumentFuture } from "../../../types/module";
import { ReadEventArgumentExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileReadEventArgument(future: ReadEventArgumentFuture, executionState: ReadEventArgumentExecutionState, context: ReconciliationContext): ReconciliationFutureResult;
//# sourceMappingURL=reconcileReadEventArgument.d.ts.map