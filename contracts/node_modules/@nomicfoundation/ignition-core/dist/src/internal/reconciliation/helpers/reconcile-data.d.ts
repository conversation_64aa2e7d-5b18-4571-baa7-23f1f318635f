import { SendDataFuture } from "../../../types/module";
import { SendDataExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResultFailure } from "../types";
export declare function reconcileData(future: SendDataFuture, exState: SendDataExecutionState, context: ReconciliationContext): ReconciliationFutureResultFailure | undefined;
//# sourceMappingURL=reconcile-data.d.ts.map