{"version": 3, "file": "reconcile-strategy.js", "sourceRoot": "", "sources": ["../../../../../src/internal/reconciliation/helpers/reconcile-strategy.ts"], "names": [], "mappings": ";;;AAaA,2EAS+C;AAK/C,oCAAgC;AAEhC,SAAgB,iBAAiB,CAC/B,MAW2B,EAC3B,OAOmC,EACnC,OAA8B;IAE9B;;;;;;OAMG;IACH,IAAI,OAAO,CAAC,MAAM,KAAK,iCAAe,CAAC,OAAO,EAAE;QAC9C,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,kBAAkB,GAAG,OAAO,CAAC,QAAQ,CAAC;IAC5C,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;IAEzC,IAAI,kBAAkB,KAAK,eAAe,EAAE;QAC1C,OAAO,IAAA,YAAI,EACT,MAAM,EACN,0BAA0B,kBAAkB,SAAS,eAAe,GAAG,CACxE,CAAC;KACH;IAED,wEAAwE;IACxE,8DAA8D;IAC9D,MAAM,oBAAoB,GAAG,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC;IAC1D,MAAM,iBAAiB,GAAG,OAAO,CAAC,cAAc,CAAC;IAEjD,MAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAoC,CAAC;IAC7E,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,EAAE;QACrD,OAAO,IAAA,YAAI,EACT,MAAM,EACN,gCAAgC,IAAI,CAAC,SAAS,CAC5C,oBAAoB,CACrB,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAC5C,CAAC;KACH;AACH,CAAC;AA1DD,8CA0DC"}