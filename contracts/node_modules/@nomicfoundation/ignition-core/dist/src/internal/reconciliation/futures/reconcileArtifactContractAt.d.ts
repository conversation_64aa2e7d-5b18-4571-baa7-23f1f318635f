import { ContractAtFuture } from "../../../types/module";
import { ContractAtExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileArtifactContractAt(future: ContractAtFuture, executionState: ContractAtExecutionState, context: ReconciliationContext): Promise<ReconciliationFutureResult>;
//# sourceMappingURL=reconcileArtifactContractAt.d.ts.map