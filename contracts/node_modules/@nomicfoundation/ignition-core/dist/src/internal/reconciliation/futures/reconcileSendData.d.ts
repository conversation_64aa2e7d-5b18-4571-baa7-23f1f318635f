import { SendDataFuture } from "../../../types/module";
import { SendDataExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileSendData(future: SendDataFuture, executionState: SendDataExecutionState, context: ReconciliationContext): ReconciliationFutureResult;
//# sourceMappingURL=reconcileSendData.d.ts.map