import { EncodeFunctionCallFuture } from "../../../types/module";
import { EncodeFunctionCallExecutionState } from "../../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "../types";
export declare function reconcileNamedEncodeFunctionCall(future: EncodeFunctionCallFuture<string, string>, executionState: EncodeFunctionCallExecutionState, context: ReconciliationContext): ReconciliationFutureResult;
//# sourceMappingURL=reconcileNamedEncodeFunctionCall.d.ts.map