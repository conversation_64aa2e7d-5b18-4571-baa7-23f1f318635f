import { DeploymentState } from "../../execution/types/deployment-state";
import { CallExecutionState, DeploymentExecutionState, SendDataExecutionState, StaticCallExecutionState } from "../../execution/types/execution-state";
export declare function getNetworkExecutionStates(deploymentState: DeploymentState): Array<DeploymentExecutionState | CallExecutionState | SendDataExecutionState | StaticCallExecutionState>;
//# sourceMappingURL=get-network-execution-states.d.ts.map