"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.replayStrategy = void 0;
const assertions_1 = require("../../../utils/assertions");
const execution_state_1 = require("../../types/execution-state");
const execution_strategy_1 = require("../../types/execution-strategy");
const network_interaction_1 = require("../../types/network-interaction");
/**
 * This function creates and replays an ExecutionStrategy generator, and
 * is meant to be used in these situations:
 *  - An execution state is starting to be run.
 *  - The execution engine got a new result for a network interaction and
 *    wants to process it.
 *  - The execution engine wants to resend a transaction, and hence,
 *    re-simulate it.
 *
 * The ExecutionState must not be completed yet.
 *
 * If the ExecutionState has no NetworkInteraction, a new generator is returned.
 */
async function replayExecutionStrategyWithOnchainInteractions(executionState, strategy) {
    (0, assertions_1.assertIgnitionInvariant)(executionState.status === execution_state_1.ExecutionStatus.STARTED, `Unexpected completed execution state ${executionState.id} when replaying it.`);
    let generator;
    switch (executionState.type) {
        case execution_state_1.ExecutionStateType.DEPLOYMENT_EXECUTION_STATE:
            generator = strategy.executeDeployment(executionState);
            break;
        case execution_state_1.ExecutionStateType.CALL_EXECUTION_STATE:
            generator = strategy.executeCall(executionState);
            break;
        case execution_state_1.ExecutionStateType.SEND_DATA_EXECUTION_STATE:
            generator = strategy.executeSendData(executionState);
            break;
    }
    const networkInteractions = executionState.networkInteractions;
    if (networkInteractions.length === 0) {
        return generator;
    }
    let generatorResult = await generator.next();
    for (let i = 0; i < networkInteractions.length - 1; i++) {
        const interaction = networkInteractions[i];
        assertValidGeneratorResult(executionState.id, interaction, generatorResult, true);
        if (interaction.type === network_interaction_1.NetworkInteractionType.STATIC_CALL) {
            generatorResult = await generator.next(interaction.result);
        }
        else {
            const confirmedTx = interaction.transactions.find((tx) => tx.receipt !== undefined);
            generatorResult = await generator.next({
                type: execution_strategy_1.OnchainInteractionResponseType.SUCCESSFUL_TRANSACTION,
                transaction: confirmedTx,
            });
        }
    }
    const lastInteraction = networkInteractions[networkInteractions.length - 1];
    assertValidGeneratorResult(executionState.id, lastInteraction, generatorResult);
    return generator;
}
/**
 * This function is the StaticCall-only version of replayExecutionStrategyWithOnchainInteractions.
 */
async function replayStaticCallExecutionStrategy(executionState, strategy) {
    (0, assertions_1.assertIgnitionInvariant)(executionState.status === execution_state_1.ExecutionStatus.STARTED, `Unexpected completed execution state ${executionState.id} when replaying it.`);
    const generator = strategy.executeStaticCall(executionState);
    const networkInteractions = executionState.networkInteractions;
    if (networkInteractions.length === 0) {
        return generator;
    }
    let generatorResult = await generator.next();
    for (let i = 0; i < networkInteractions.length - 1; i++) {
        const interaction = networkInteractions[i];
        assertValidGeneratorResult(executionState.id, interaction, generatorResult, true);
        generatorResult = await generator.next(interaction.result);
    }
    const lastInteraction = networkInteractions[networkInteractions.length - 1];
    assertValidGeneratorResult(executionState.id, lastInteraction, generatorResult);
    return generator;
}
/**
 * This function returns a strategy generator for the executionState that has been replayed
 * up to the request that lead to the last network interaction of the executionState being
 * created.
 *
 * IMPORTANT: This function is NOT type-safe. It is the responsibility of the caller to
 * interpret the returned generator as the correct type. This is allows us to have a single
 * function replay all the different types of execution states.
 *
 * @param executionState The execution state.
 * @param strategy The strategy to use to create the generator.
 * @returns The replayed strategy generator.
 */
async function replayStrategy(executionState, strategy) {
    switch (executionState.type) {
        case execution_state_1.ExecutionStateType.DEPLOYMENT_EXECUTION_STATE:
            return replayExecutionStrategyWithOnchainInteractions(executionState, strategy);
        case execution_state_1.ExecutionStateType.CALL_EXECUTION_STATE:
            return replayExecutionStrategyWithOnchainInteractions(executionState, strategy);
        case execution_state_1.ExecutionStateType.SEND_DATA_EXECUTION_STATE:
            return replayExecutionStrategyWithOnchainInteractions(executionState, strategy);
        case execution_state_1.ExecutionStateType.STATIC_CALL_EXECUTION_STATE:
            return replayStaticCallExecutionStrategy(executionState, strategy);
    }
}
exports.replayStrategy = replayStrategy;
function assertValidGeneratorResult(executionStateId, interaction, generatorResult, shouldBeResolved) {
    (0, assertions_1.assertIgnitionInvariant)(generatorResult.done !== true, `Unexpected strategy finalization when replaying ${executionStateId}/${interaction.id}`);
    (0, assertions_1.assertIgnitionInvariant)(generatorResult.value.type !== execution_strategy_1.SIMULATION_SUCCESS_SIGNAL_TYPE, `Unexpected ${execution_strategy_1.SIMULATION_SUCCESS_SIGNAL_TYPE} when replaying ${executionStateId}/${interaction.id}`);
    (0, assertions_1.assertIgnitionInvariant)(interaction.type === generatorResult.value.type, `Unexpected difference between execution strategy request and wat was already executed while replaying ${executionStateId}/${interaction.id}`);
    if (shouldBeResolved === undefined) {
        return;
    }
    if (interaction.type === network_interaction_1.NetworkInteractionType.STATIC_CALL) {
        (0, assertions_1.assertIgnitionInvariant)(interaction.result !== undefined, `Unexpected unresolved StaticCall request when replaying ${executionStateId}/${interaction.id}`);
        return;
    }
    const confirmedTx = interaction.transactions.find((tx) => tx.receipt !== undefined);
    (0, assertions_1.assertIgnitionInvariant)(confirmedTx !== undefined, `Unexpected unresolved OnchainInteraction request when replaying ${executionStateId}/${interaction.id}`);
    (0, assertions_1.assertIgnitionInvariant)(confirmedTx.receipt !== undefined, `Unexpected unresolved OnchainInteraction request when replaying ${executionStateId}/${interaction.id}`);
}
//# sourceMappingURL=replay-strategy.js.map