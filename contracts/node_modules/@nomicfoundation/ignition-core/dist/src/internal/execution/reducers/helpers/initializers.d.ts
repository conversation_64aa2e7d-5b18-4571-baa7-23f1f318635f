import { CallExecutionState, ContractAtExecutionState, DeploymentExecutionState, EncodeFunction<PERSON>allExecutionState, ReadEventArgumentExecutionState, SendDataExecutionState, StaticCallExecutionState } from "../../types/execution-state";
import { CallExecutionStateInitializeMessage, ContractAtExecutionStateInitializeMessage, DeploymentExecutionStateInitializeMessage, EncodeFunctionCallExecutionStateInitializeMessage, ReadEventArgExecutionStateInitializeMessage, SendDataExecutionStateInitializeMessage, StaticCallExecutionStateInitializeMessage } from "../../types/messages";
export declare function initialiseDeploymentExecutionStateFrom(action: DeploymentExecutionStateInitializeMessage): DeploymentExecutionState;
export declare function initialiseStaticCallExecutionStateFrom(action: StaticCallExecutionStateInitializeMessage): StaticCallExecutionState;
export declare function initialiseSendDataExecutionStateFrom(action: SendDataExecutionStateInitializeMessage): SendDataExecutionState;
export declare function initialiseReadEventArgumentExecutionStateFrom(action: ReadEventArgExecutionStateInitializeMessage): ReadEventArgumentExecutionState;
export declare function initialiseContractAtExecutionStateFrom(action: ContractAtExecutionStateInitializeMessage): ContractAtExecutionState;
export declare function initialiseEncodeFunctionCallExecutionStateFrom(action: EncodeFunctionCallExecutionStateInitializeMessage): EncodeFunctionCallExecutionState;
export declare function initialiseCallExecutionStateFrom(action: CallExecutionStateInitializeMessage): CallExecutionState;
//# sourceMappingURL=initializers.d.ts.map