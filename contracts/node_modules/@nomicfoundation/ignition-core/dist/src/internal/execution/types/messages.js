"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JournalMessageType = void 0;
/**
 * NOTE:
 *
 * when adding/removing/changing any of these
 * be sure to update UiEventType accordingly
 */
var JournalMessageType;
(function (JournalMessageType) {
    JournalMessageType["DEPLOYMENT_INITIALIZE"] = "DEPLOYMENT_INITIALIZE";
    JournalMessageType["RUN_START"] = "RUN_START";
    JournalMessageType["WIPE_APPLY"] = "WIPE_APPLY";
    JournalMessageType["DEPLOYMENT_EXECUTION_STATE_INITIALIZE"] = "DEPLOYMENT_EXECUTION_STATE_INITIALIZE";
    JournalMessageType["DEPLOYMENT_EXECUTION_STATE_COMPLETE"] = "DEPLOYMENT_EXECUTION_STATE_COMPLETE";
    JournalMessageType["CALL_EXECUTION_STATE_INITIALIZE"] = "CALL_EXECUTION_STATE_INITIALIZE";
    JournalMessageType["CALL_EXECUTION_STATE_COMPLETE"] = "CALL_EXECUTION_STATE_COMPLETE";
    JournalMessageType["STATIC_CALL_EXECUTION_STATE_INITIALIZE"] = "STATIC_CALL_EXECUTION_STATE_INITIALIZE";
    JournalMessageType["STATIC_CALL_EXECUTION_STATE_COMPLETE"] = "STATIC_CALL_EXECUTION_STATE_COMPLETE";
    JournalMessageType["SEND_DATA_EXECUTION_STATE_INITIALIZE"] = "SEND_DATA_EXECUTION_STATE_INITIALIZE";
    JournalMessageType["SEND_DATA_EXECUTION_STATE_COMPLETE"] = "SEND_DATA_EXECUTION_STATE_COMPLETE";
    JournalMessageType["CONTRACT_AT_EXECUTION_STATE_INITIALIZE"] = "CONTRACT_AT_EXECUTION_STATE_INITIALIZE";
    JournalMessageType["READ_EVENT_ARGUMENT_EXECUTION_STATE_INITIALIZE"] = "READ_EVENT_ARGUMENT_EXECUTION_STATE_INITIALIZE";
    JournalMessageType["ENCODE_FUNCTION_CALL_EXECUTION_STATE_INITIALIZE"] = "ENCODE_FUNCTION_CALL_EXECUTION_STATE_INITIALIZE";
    JournalMessageType["NETWORK_INTERACTION_REQUEST"] = "NETWORK_INTERACTION_REQUEST";
    JournalMessageType["TRANSACTION_PREPARE_SEND"] = "TRANSACTION_PREPARE_SEND";
    JournalMessageType["TRANSACTION_SEND"] = "TRANSACTION_SEND";
    JournalMessageType["TRANSACTION_CONFIRM"] = "TRANSACTION_CONFIRM";
    JournalMessageType["STATIC_CALL_COMPLETE"] = "STATIC_CALL_COMPLETE";
    JournalMessageType["ONCHAIN_INTERACTION_BUMP_FEES"] = "ONCHAIN_INTERACTION_BUMP_FEES";
    JournalMessageType["ONCHAIN_INTERACTION_DROPPED"] = "ONCHAIN_INTERACTION_DROPPED";
    JournalMessageType["ONCHAIN_INTERACTION_REPLACED_BY_USER"] = "ONCHAIN_INTERACTION_REPLACED_BY_USER";
    JournalMessageType["ONCHAIN_INTERACTION_TIMEOUT"] = "ONCHAIN_INTERACTION_TIMEOUT";
})(JournalMessageType = exports.JournalMessageType || (exports.JournalMessageType = {}));
//# sourceMappingURL=messages.js.map