{"version": 3, "file": "replay-strategy.js", "sourceRoot": "", "sources": ["../../../../../../src/internal/execution/future-processor/helpers/replay-strategy.ts"], "names": [], "mappings": ";;;AAAA,0DAAoE;AACpE,iEAOqC;AACrC,uEASwC;AACxC,yEAGyC;AAEzC;;;;;;;;;;;;GAYG;AACH,KAAK,UAAU,8CAA8C,CAC3D,cAG0B,EAC1B,QAA2B;IAM3B,IAAA,oCAAuB,EACrB,cAAc,CAAC,MAAM,KAAK,iCAAe,CAAC,OAAO,EACjD,wCAAwC,cAAc,CAAC,EAAE,qBAAqB,CAC/E,CAAC;IAEF,IAAI,SAGyB,CAAC;IAE9B,QAAQ,cAAc,CAAC,IAAI,EAAE;QAC3B,KAAK,oCAAkB,CAAC,0BAA0B;YAChD,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACvD,MAAM;QACR,KAAK,oCAAkB,CAAC,oBAAoB;YAC1C,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YACjD,MAAM;QACR,KAAK,oCAAkB,CAAC,yBAAyB;YAC/C,SAAS,GAAG,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YACrD,MAAM;KACT;IAED,MAAM,mBAAmB,GAAG,cAAc,CAAC,mBAAmB,CAAC;IAE/D,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;QACpC,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,eAAe,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;IAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACvD,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC3C,0BAA0B,CACxB,cAAc,CAAC,EAAE,EACjB,WAAW,EACX,eAAe,EACf,IAAI,CACL,CAAC;QAEF,IAAI,WAAW,CAAC,IAAI,KAAK,4CAAsB,CAAC,WAAW,EAAE;YAC3D,eAAe,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAO,CAAC,CAAC;SAC7D;aAAM;YACL,MAAM,WAAW,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAC/C,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,KAAK,SAAS,CACjC,CAAC;YAEF,eAAe,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC;gBACrC,IAAI,EAAE,mDAA8B,CAAC,sBAAsB;gBAC3D,WAAW,EAAE,WAAW;aACA,CAAC,CAAC;SAC7B;KACF;IAED,MAAM,eAAe,GAAG,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE5E,0BAA0B,CACxB,cAAc,CAAC,EAAE,EACjB,eAAe,EACf,eAAe,CAChB,CAAC;IAEF,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,iCAAiC,CAC9C,cAAwC,EACxC,QAA2B;IAE3B,IAAA,oCAAuB,EACrB,cAAc,CAAC,MAAM,KAAK,iCAAe,CAAC,OAAO,EACjD,wCAAwC,cAAc,CAAC,EAAE,qBAAqB,CAC/E,CAAC;IAEF,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAE7D,MAAM,mBAAmB,GAAG,cAAc,CAAC,mBAAmB,CAAC;IAE/D,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;QACpC,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,eAAe,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;IAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACvD,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC3C,0BAA0B,CACxB,cAAc,CAAC,EAAE,EACjB,WAAW,EACX,eAAe,EACf,IAAI,CACL,CAAC;QAEF,eAAe,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAO,CAAC,CAAC;KAC7D;IAED,MAAM,eAAe,GAAG,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE5E,0BAA0B,CACxB,cAAc,CAAC,EAAE,EACjB,eAAe,EACf,eAAe,CAChB,CAAC;IAEF,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;;;;;;;;GAYG;AACI,KAAK,UAAU,cAAc,CAClC,cAI4B,EAC5B,QAA2B;IAO3B,QAAQ,cAAc,CAAC,IAAI,EAAE;QAC3B,KAAK,oCAAkB,CAAC,0BAA0B;YAChD,OAAO,8CAA8C,CACnD,cAAc,EACd,QAAQ,CACT,CAAC;QACJ,KAAK,oCAAkB,CAAC,oBAAoB;YAC1C,OAAO,8CAA8C,CACnD,cAAc,EACd,QAAQ,CACT,CAAC;QACJ,KAAK,oCAAkB,CAAC,yBAAyB;YAC/C,OAAO,8CAA8C,CACnD,cAAc,EACd,QAAQ,CACT,CAAC;QACJ,KAAK,oCAAkB,CAAC,2BAA2B;YACjD,OAAO,iCAAiC,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;KACtE;AACH,CAAC;AAhCD,wCAgCC;AAED,SAAS,0BAA0B,CACjC,gBAAwB,EACxB,WAA+B,EAC/B,eAG0D,EAC1D,gBAAuB;IAEvB,IAAA,oCAAuB,EACrB,eAAe,CAAC,IAAI,KAAK,IAAI,EAC7B,mDAAmD,gBAAgB,IAAI,WAAW,CAAC,EAAE,EAAE,CACxF,CAAC;IAEF,IAAA,oCAAuB,EACrB,eAAe,CAAC,KAAK,CAAC,IAAI,KAAK,mDAA8B,EAC7D,cAAc,mDAA8B,mBAAmB,gBAAgB,IAAI,WAAW,CAAC,EAAE,EAAE,CACpG,CAAC;IAEF,IAAA,oCAAuB,EACrB,WAAW,CAAC,IAAI,KAAK,eAAe,CAAC,KAAK,CAAC,IAAI,EAC/C,yGAAyG,gBAAgB,IAAI,WAAW,CAAC,EAAE,EAAE,CAC9I,CAAC;IAEF,IAAI,gBAAgB,KAAK,SAAS,EAAE;QAClC,OAAO;KACR;IAED,IAAI,WAAW,CAAC,IAAI,KAAK,4CAAsB,CAAC,WAAW,EAAE;QAC3D,IAAA,oCAAuB,EACrB,WAAW,CAAC,MAAM,KAAK,SAAS,EAChC,2DAA2D,gBAAgB,IAAI,WAAW,CAAC,EAAE,EAAE,CAChG,CAAC;QAEF,OAAO;KACR;IAED,MAAM,WAAW,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAC/C,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,KAAK,SAAS,CACjC,CAAC;IAEF,IAAA,oCAAuB,EACrB,WAAW,KAAK,SAAS,EACzB,mEAAmE,gBAAgB,IAAI,WAAW,CAAC,EAAE,EAAE,CACxG,CAAC;IAEF,IAAA,oCAAuB,EACrB,WAAW,CAAC,OAAO,KAAK,SAAS,EACjC,mEAAmE,gBAAgB,IAAI,WAAW,CAAC,EAAE,EAAE,CACxG,CAAC;AACJ,CAAC"}