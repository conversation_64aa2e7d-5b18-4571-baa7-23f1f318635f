import { EIP1193Provider } from "../../types/provider";
import { NetworkFees, RawStaticCallResult, FullTransaction, Transaction, TransactionReceipt } from "./types/jsonrpc";
/**
 * The params to make an `eth_call`.
 */
export interface CallParams {
    to?: string;
    value: bigint;
    data: string;
    from: string;
    nonce?: number;
    fees?: NetworkFees;
    gasLimit?: bigint;
}
/**
 * The params to send a transaction.
 */
export interface TransactionParams {
    to?: string;
    value: bigint;
    data: string;
    from: string;
    nonce: number;
    fees: NetworkFees;
    gasLimit: bigint;
}
/**
 * The params to estimate the gas of a transaction.
 */
export interface EstimateGasParams extends Omit<TransactionParams, "gasLimit" | "fees"> {
    fees?: NetworkFees;
}
/**
 * An Ethereum block.
 */
export interface Block {
    hash: string;
    number: number;
    baseFeePerGas?: bigint;
}
/**
 * This interface has methods for every JSON-RPC call that we need.
 */
export interface JsonRpcClient {
    /**
     * Returns the chain ID of the network.
     */
    getChainId: () => Promise<number>;
    /**
     * Returns the recommended for the network fees.
     */
    getNetworkFees: () => Promise<NetworkFees>;
    /**
     * Returns the latest block.
     */
    getLatestBlock: () => Promise<Block>;
    /**
     * Returns the balance of an account.
     *
     * @param address The account's address.
     * @param blockTag Whether we should fetch the latest block balance or the
     * pending balance.
     */
    getBalance: (address: string, blockTag: "latest" | "pending") => Promise<bigint>;
    /**
     * Update the balance of the account. Only relevant for local development
     * chains.
     *
     * @param address The account's address.
     * @param balance The balance to set the account to.
     * @returns Whether the update was applied.
     */
    setBalance: (address: string, balance: bigint) => Promise<boolean>;
    /**
     * Performs an `eth_call` JSON-RPC request, and returns the result or an error
     * object with the return data and a boolean indicating if the request failed
     * with an error message that telling that the call failed with a custom
     * error.
     *
     * @param callParams The params for the call.
     * @param blockTag The block tag to use for the call.
     */
    call: (callParams: CallParams, blockTag: "latest" | "pending") => Promise<RawStaticCallResult>;
    /**
     * Estimates the gas required to execute a transaction.
     *
     * @param transactionParams The transaction parameters, excluding gasLimit.
     */
    estimateGas: (transactionParams: EstimateGasParams) => Promise<bigint>;
    /**
     * Sends a transaction to the Ethereum network and returns its hash,
     * if the transaction is valid and accepted in the node's mempool.
     *
     * In automined networks eth_sendTransaction may still fail while accepting
     * a transaction in its mempool. In those cases, this function will still
     * return its hash, ignoring any error information.
     *
     * @param transactionParams The parameters of the transaction to send.
     */
    sendTransaction: (transactionParams: TransactionParams) => Promise<string>;
    /**
     * Sends a presigned raw transaction to the Ethereum network and returns
     * its hash, if the transaction is valid and accepted in the node's mempool.
     *
     * @param presignedTx the presigned transaction to send
     * @returns the hash of the transaction.
     */
    sendRawTransaction: (presignedTx: string) => Promise<string>;
    /**
     * Returns the transaction count of an account.
     *
     * @param address The account's address.
     * @param blockTag The block to use for the count. If "pending", the mempool
     * is taken into account.
     */
    getTransactionCount: (address: string, blockTag: "pending" | "latest" | number) => Promise<number>;
    /**
     * Returns a transaction, or undefined if it doesn't exist.
     *
     * @param txHash The transaction hash.
     */
    getTransaction: (txHash: string) => Promise<Omit<Transaction, "receipt"> | undefined>;
    /**
     * Returns a transaction's receipt, or undefined if the transaction doesn't
     * exist or it hasn't confirmed yet.
     *
     * @param txHash The transaction's hash.
     */
    getTransactionReceipt: (txHash: string) => Promise<TransactionReceipt | undefined>;
    /**
     * Returns the deployed bytecode of the contract at the given address.
     *
     * If the address is not a contract or it does not have bytecode the returned
     * result will be "0x".
     *
     * @param address the address of the contract
     * @returns the deployed bytecode of the contract
     */
    getCode: (address: string) => Promise<string>;
}
/**
 * A JsonRpcClient that uses an EIP-1193 provider to make the calls.
 */
export declare class EIP1193JsonRpcClient implements JsonRpcClient {
    private readonly _provider;
    private readonly _config?;
    constructor(_provider: EIP1193Provider, _config?: {
        maxFeePerGasLimit?: bigint | undefined;
        maxFeePerGas?: bigint | undefined;
        maxPriorityFeePerGas?: bigint | undefined;
        gasPrice?: bigint | undefined;
    } | undefined);
    getChainId(): Promise<number>;
    getNetworkFees(): Promise<NetworkFees>;
    getLatestBlock(): Promise<Block>;
    getBalance(address: string, blockTag: "latest" | "pending"): Promise<bigint>;
    setBalance(address: string, balance: bigint): Promise<boolean>;
    call(callParams: CallParams, blockTag: "latest" | "pending"): Promise<RawStaticCallResult>;
    estimateGas(estimateGasParams: EstimateGasParams): Promise<bigint>;
    sendTransaction(transactionParams: TransactionParams): Promise<string>;
    sendRawTransaction(presignedTx: string): Promise<string>;
    getTransactionCount(address: string, blockTag: number | "latest" | "pending"): Promise<number>;
    /**
     * Like `getTransaction`, but returns the full transaction object.
     */
    getFullTransaction(txHash: string): Promise<FullTransaction | undefined>;
    getTransaction(txHash: string): Promise<Omit<Transaction, "receipt"> | undefined>;
    getTransactionReceipt(txHash: string): Promise<TransactionReceipt | undefined>;
    getCode(address: string): Promise<string>;
    private _getNetworkFees;
    /**
     * The max fee per gas is needed in the max fee calculation.
     *
     * It is resolved from config if present, falling back to
     * the  `eth_maxPriorityFeePerGas` RPC call if supported by the chain,
     * and finally falling back to the default max fee per gas.
     *
     * @returns a max fee per gas based on the config, RPC call, or default value.
     */
    private _resolveMaxPriorityFeePerGas;
    private _getMaxPriorityFeePerGas;
}
//# sourceMappingURL=jsonrpc-client.d.ts.map