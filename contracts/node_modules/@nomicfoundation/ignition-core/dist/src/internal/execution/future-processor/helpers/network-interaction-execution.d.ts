/**
 * This files contains the utility functions that the execution engine should
 * use to interact with the network during the execution of a NetworkInteraction.
 *
 * @file
 */
import { DeploymentLoader } from "../../../deployment-loader/types";
import { JsonRpcClient } from "../../jsonrpc-client";
import { NonceManager } from "../../nonce-management/json-rpc-nonce-manager";
import { SimulationErrorExecutionResult, StrategySimulationErrorExecutionResult } from "../../types/execution-result";
import { RawStaticCallResult, Transaction } from "../../types/jsonrpc";
import { OnchainInteraction, StaticCall } from "../../types/network-interaction";
/**
 * Runs a StaticCall NetworkInteraction to completion, returning its raw result.
 *
 * @param client The JsonRpcClient to use to interact with the network.
 * @param staticCall The StaticCall to run.
 * @returns The raw result of the StaticCall.
 */
export declare function runStaticCall(client: <PERSON>sonRpc<PERSON><PERSON>, staticCall: StaticCall): Promise<RawStaticCallResult>;
/**
 * The type of a successful response from `sendTransactionForOnchainInteraction`.
 */
export declare const TRANSACTION_SENT_TYPE = "TRANSACTION";
/**
 * Sends the a transaction to run an OnchainInteraction.
 *
 * If this is the first transaction being sent for the OnchainInteraction, the
 * nonce will be fetched using the provided callback.
 *
 * This function estimates gas and runs a simulation before sending the transaction.
 *
 * Simulations are run both in the case of a failed gas estimation (to report
 * why it failed), and in the case of a successful gas estimation (to make sure
 * the transaction will not fail, and report any error).
 *
 * This function is meant to be used in conjunction with the ExecutionStrategy's
 * generator that requested the OnchainInteraction, as is its responsibility to
 * decode the result of the simulation. The `decodeSimulationResult` callback is
 * more generic to make this function easier to test, but it should normally
 * call the generator's `next` and return its result, replacing
 * `SimulationSuccessSignal` results with `undefined`.
 *
 * Note that if we are resending a transaction for an OnchainInteraction, we
 * need a new ExecutionStrategy generator to decode the simulation result, as
 * the previous one will be waiting for a confirmed transaction.
 *
 * This function can be used in these cases:
 * - When the OnchainInteraction needs to start being executed (i.e. first tx).
 * - When we detected a dropped transaction and we need to resend it with the
 * same nonce.
 * - When we want to bump the fees of a transaction.
 *
 * This function MUST NOT be used in these cases:
 * - When we detected a dropped transaction and we need to resend it with a
 * different nonce.
 *
 * @param client The JsonRpcClient to use to interact with the network.
 * @param sender The account to send the transaction from.
 * @param onchainInteraction The OnchainInteraction to send the transaction for.
 * @param getNonce A callback to fetch the nonce for the transaction.
 * @param decodeSimulationResult A callback to decode the result of the simulation.
 *  This callback should return undefined if the simulation was successful, or
 *  a SimulationErrorExecutionResult or StrategyErrorExecutionResult if the
 *  simulation failed.
 * @returns Any error returned by decodeSimulationResult or an object with the
 *  transaction hash and nonce.
 */
export declare function sendTransactionForOnchainInteraction(client: JsonRpcClient, sender: string, onchainInteraction: OnchainInteraction, nonceManager: NonceManager, decodeSimulationResult: (simulationResult: RawStaticCallResult) => Promise<SimulationErrorExecutionResult | StrategySimulationErrorExecutionResult | undefined>, deploymentLoader: DeploymentLoader, futureId: string): Promise<SimulationErrorExecutionResult | StrategySimulationErrorExecutionResult | {
    type: typeof TRANSACTION_SENT_TYPE;
    transaction: Pick<Transaction, "hash" | "fees">;
    nonce: number;
}>;
//# sourceMappingURL=network-interaction-execution.d.ts.map