import { ExecutionState } from "../types/execution-state";
import { CallExecutionStateCompleteMessage, CallExecutionStateInitializeMessage, ContractAtExecutionStateInitializeMessage, DeploymentExecutionStateCompleteMessage, DeploymentExecutionStateInitializeMessage, EncodeFunctionCallExecutionStateInitializeMessage, NetworkInteractionRequestMessage, OnchainInteractionBumpFeesMessage, OnchainInteractionDroppedMessage, OnchainInteractionReplacedByUserMessage, OnchainInteractionTimeoutMessage, ReadEventArgExecutionStateInitializeMessage, SendDataExecutionStateCompleteMessage, SendDataExecutionStateInitializeMessage, StaticCallCompleteMessage, StaticCallExecutionStateCompleteMessage, StaticCallExecutionStateInitializeMessage, TransactionConfirmMessage, TransactionPrepareSendMessage, TransactionSendMessage } from "../types/messages";
export declare function executionStateReducer(state: ExecutionState | undefined, action: DeploymentExecutionStateInitializeMessage | DeploymentExecutionStateCompleteMessage | CallExecutionStateInitializeMessage | CallExecutionStateCompleteMessage | StaticCallExecutionStateInitializeMessage | StaticCallExecutionStateCompleteMessage | SendDataExecutionStateInitializeMessage | SendDataExecutionStateCompleteMessage | ContractAtExecutionStateInitializeMessage | ReadEventArgExecutionStateInitializeMessage | EncodeFunctionCallExecutionStateInitializeMessage | NetworkInteractionRequestMessage | TransactionPrepareSendMessage | TransactionSendMessage | TransactionConfirmMessage | StaticCallCompleteMessage | OnchainInteractionBumpFeesMessage | OnchainInteractionDroppedMessage | OnchainInteractionReplacedByUserMessage | OnchainInteractionTimeoutMessage): ExecutionState;
//# sourceMappingURL=execution-state-reducer.d.ts.map