{"version": 3, "file": "type-guards.d.ts", "sourceRoot": "", "sources": ["../../src/type-guards.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EACL,mBAAmB,EACnB,uBAAuB,EACvB,sBAAsB,EACtB,gBAAgB,EAChB,wBAAwB,EACxB,cAAc,EACd,gBAAgB,EAChB,wBAAwB,EACxB,kBAAkB,EAClB,MAAM,EACN,UAAU,EACV,uBAAuB,EACvB,2BAA2B,EAC3B,6BAA6B,EAC7B,qCAAqC,EACrC,oCAAoC,EACpC,uBAAuB,EACvB,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EACjB,MAAM,gBAAgB,CAAC;AAiBxB;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,SAAS,EAAE,OAAO,GAAG,SAAS,IAAI,QAAQ,CAaxE;AAED;;;;GAIG;AACH,wBAAgB,YAAY,CAAC,SAAS,EAAE,OAAO,GAAG,SAAS,IAAI,UAAU,CAIxE;AAED;;;;GAIG;AACH,wBAAgB,QAAQ,CAAC,SAAS,EAAE,OAAO,GAAG,SAAS,IAAI,MAAM,CAOhE;AAED;;;;GAIG;AACH,wBAAgB,gBAAgB,CAC9B,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,cAAc,CAAC,MAAM,CAAC,CAalC;AAED;;;;GAIG;AACH,wBAAgB,wBAAwB,CACtC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAW1C;AAED;;;;GAIG;AACH,wBAAgB,yBAAyB,CACvC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,uBAAuB,CAMnC;AAED;;;;GAIG;AACH,wBAAgB,oBAAoB,CAClC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAK9C;AAED;;;;GAIG;AACH,wBAAgB,uBAAuB,CACrC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAE5C;AAED;;;;GAIG;AACH,wBAAgB,0BAA0B,CACxC,SAAS,EAAE,OAAO,GACjB,SAAS,IAAI,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,CAIvD;AAED;;;;GAIG;AACH,wBAAgB,yBAAyB,CACvC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,uBAAuB,CAEnC;AAED;;;;GAIG;AACH,wBAAgB,+BAA+B,CAC7C,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,qCAAqC,CAAC,MAAM,CAAC,CAEzD;AAED;;;;GAIG;AACH,wBAAgB,kCAAkC,CAChD,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,wBAAwB,CAEpC;AAED;;;;GAIG;AACH,wBAAgB,8BAA8B,CAC5C,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,oCAAoC,CAAC,MAAM,CAAC,CAExD;AAED;;;;GAIG;AACH,wBAAgB,iCAAiC,CAC/C,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,uBAAuB,CAEnC;AAED;;;;GAIG;AACH,wBAAgB,uBAAuB,CACrC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,6BAA6B,CAAC,MAAM,CAAC,CAEjD;AAED;;;;GAIG;AACH,wBAAgB,0BAA0B,CACxC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,gBAAgB,CAE5B;AAED;;;;GAIG;AACH,wBAAgB,gBAAgB,CAC9B,SAAS,EAAE,OAAO,GACjB,SAAS,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAY/C;AAED;;;;GAIG;AACH,wBAAgB,kBAAkB,CAChC,MAAM,EAAE,MAAM,GACb,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAEpC;AAED;;;;GAIG;AACH,wBAAgB,qCAAqC,CACnD,CAAC,EAAE,MAAM,GACR,CAAC,IAAI,OAAO,CACb,OAAO,CACL,OAAO,CACL,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,EACjD,uBAAuB,CACxB,EACD,6BAA6B,CAAC,MAAM,CAAC,CACtC,EACD,gBAAgB,CACjB,CAOA;AAED;;;;GAIG;AACH,wBAAgB,kBAAkB,CAChC,SAAS,EAAE,OAAO,GACjB,SAAS,IAAI,gBAAgB,CAK/B;AAED;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,SAAS,EAAE,OAAO,GAAG,SAAS,IAAI,YAAY,CAO5E;AAED;;;;GAIG;AACH,wBAAgB,qBAAqB,CACnC,SAAS,EAAE,OAAO,GACjB,SAAS,IAAI,mBAAmB,CAIlC;AAED;;;;GAIG;AACH,wBAAgB,6BAA6B,CAC3C,SAAS,EAAE,OAAO,GACjB,SAAS,IAAI,2BAA2B,CAAC,GAAG,CAAC,CAK/C"}