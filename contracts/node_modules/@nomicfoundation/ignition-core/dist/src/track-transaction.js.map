{"version": 3, "file": "track-transaction.js", "sourceRoot": "", "sources": ["../../src/track-transaction.ts"], "names": [], "mappings": ";;;AAAA,uCAAsC;AAEtC,qCAAyC;AACzC,gGAA2F;AAC3F,wDAAgD;AAChD,4FAGuD;AACvD,wEAA2E;AAE3E,kEAI6C;AAK7C,4DAAyD;AACzD,4DAAsE;AAOtE,wFAGwD;AAExD,gHAA0G;AAE1G;;;;;;;;;;GAUG;AACI,KAAK,UAAU,gBAAgB,CACpC,aAAqB,EACrB,MAAc,EACd,QAAyB,EACzB,wBAAgC,6BAAa,CAAC,qBAAqB,EACnE,oBAIoB,0CAAe;IAEnC,IAAI,CAAC,CAAC,MAAM,IAAA,qBAAU,EAAC,aAAa,CAAC,CAAC,EAAE;QACtC,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,iBAAiB,CAAC,wBAAwB,EAAE;YACzE,aAAa;SACd,CAAC,CAAC;KACJ;IACD,MAAM,gBAAgB,GAAG,IAAI,6CAAoB,CAAC,aAAa,CAAC,CAAC;IAEjE,MAAM,eAAe,GAAG,MAAM,IAAA,8CAAmB,EAAC,gBAAgB,CAAC,CAAC;IAEpE,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,iBAAiB,CAAC,wBAAwB,EAAE;YACzE,aAAa;SACd,CAAC,CAAC;KACJ;IAED,MAAM,aAAa,GAAG,IAAI,qCAAoB,CAAC,QAAQ,CAAC,CAAC;IAEzD,MAAM,WAAW,GAAG,MAAM,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAEnE,IAAI,WAAW,KAAK,SAAS,EAAE;QAC7B,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,iBAAiB,CAAC,qBAAqB,EAAE;YACtE,MAAM;SACP,CAAC,CAAC;KACJ;IAED,MAAM,QAAQ,GAAG,IAAA,wDAAyB,EAAC,eAAe,CAAC,CAAC;IAE5D;;;;;;;;OAQG;IACH,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,KAAK,MAAM,kBAAkB,IAAI,OAAO,CAAC,mBAAmB,EAAE;YAC5D,IACE,kBAAkB,CAAC,IAAI;gBACrB,4CAAsB,CAAC,mBAAmB;gBAC5C,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;gBAC7D,kBAAkB,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,EAC9C;gBACA,IAAI,kBAAkB,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;oBAChD,kEAAkE;oBAClE,IACE,kBAAkB,CAAC,EAAE,EAAE,WAAW,EAAE;wBAClC,WAAW,CAAC,EAAE,EAAE,WAAW,EAAE;wBAC/B,kBAAkB,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI;wBAC5C,kBAAkB,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,EAC9C;wBACA,IAAI,IAAiB,CAAC;wBACtB,IACE,cAAc,IAAI,WAAW;4BAC7B,sBAAsB,IAAI,WAAW;4BACrC,WAAW,CAAC,YAAY,KAAK,SAAS;4BACtC,WAAW,CAAC,oBAAoB,KAAK,SAAS,EAC9C;4BACA,IAAI,GAAG;gCACL,YAAY,EAAE,WAAW,CAAC,YAAY;gCACtC,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;6BACvD,CAAC;yBACH;6BAAM;4BACL,IAAA,oCAAuB,EACrB,UAAU,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,KAAK,SAAS,EAC/D,8BAA8B,CAC/B,CAAC;4BAEF,IAAI,GAAG;gCACL,QAAQ,EAAE,WAAW,CAAC,QAAQ;6BAC/B,CAAC;yBACH;wBAED,MAAM,sBAAsB,GAA2B;4BACrD,QAAQ,EAAE,OAAO,CAAC,EAAE;4BACpB,oBAAoB,EAAE,kBAAkB,CAAC,EAAE;4BAC3C,KAAK,EAAE,kBAAkB,CAAC,KAAK;4BAC/B,IAAI,EAAE,6BAAkB,CAAC,gBAAgB;4BACzC,WAAW,EAAE;gCACX,IAAI,EAAE,WAAW,CAAC,IAAI;gCACtB,IAAI;6BACL;yBACF,CAAC;wBAEF,MAAM,iBAAiB,CACrB,sBAAsB,EACtB,eAAe,EACf,gBAAgB,CACjB,CAAC;wBAEF,OAAO;qBACR;oBACD,mEAAmE;oBACnE,yEAAyE;yBACpE;wBACH,OAAO,kBAAkB,CACvB,OAAO,EACP,kBAAkB,EAClB,WAAW,EACX,qBAAqB,EACrB,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,iBAAiB,CAClB,CAAC;qBACH;iBACF;gBACD,gGAAgG;qBAC3F;oBACH,qFAAqF;oBACrF,IAAI,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE;wBAChE,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;qBACrE;oBAED,mEAAmE;oBACnE,yEAAyE;oBACzE,OAAO,kBAAkB,CACvB,OAAO,EACP,kBAAkB,EAClB,WAAW,EACX,qBAAqB,EACrB,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,iBAAiB,CAClB,CAAC;iBACH;aACF;SACF;KACF;IAED,6DAA6D;IAC7D,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC;AAC7E,CAAC;AAjJD,4CAiJC;AAED,KAAK,UAAU,kBAAkB,CAC/B,OAI0B,EAC1B,kBAAsC,EACtC,WAA4B,EAC5B,qBAA6B,EAC7B,aAAmC,EACnC,eAAgC,EAChC,gBAAsC,EACtC,iBAAmE;IAEnE,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACzC,aAAa,CAAC,cAAc,EAAE;QAC9B,aAAa,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC;KACtD,CAAC,CAAC;IAEH,IAAA,oCAAuB,EACrB,OAAO,KAAK,SAAS,EACrB,wCAAwC,CACzC,CAAC;IAEF,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;IAE7D,IAAI,aAAa,IAAI,qBAAqB,EAAE;QAC1C,MAAM,0BAA0B,GAC9B;YACE,QAAQ,EAAE,OAAO,CAAC,EAAE;YACpB,oBAAoB,EAAE,kBAAkB,CAAC,EAAE;YAC3C,IAAI,EAAE,6BAAkB,CAAC,oCAAoC;SAC9D,CAAC;QAEJ,MAAM,iBAAiB,CACrB,0BAA0B,EAC1B,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF;;;;;;;;WAQG;QACH,OAAO,+EAA+E,OAAO,CAAC,EAAE;;8HAE0B,CAAC;KAC5H;SAAM;QACL,MAAM,IAAI,sBAAa,CACrB,oBAAM,CAAC,iBAAiB,CAAC,0BAA0B,CACpD,CAAC;KACH;AACH,CAAC"}