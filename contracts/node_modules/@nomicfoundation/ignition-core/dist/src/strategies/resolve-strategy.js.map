{"version": 3, "file": "resolve-strategy.js", "sourceRoot": "", "sources": ["../../../src/strategies/resolve-strategy.ts"], "names": [], "mappings": ";;;AAAA,sCAA0C;AAC1C,yDAAiD;AAIjD,qDAAiD;AACjD,yDAAqD;AAErD,SAAgB,eAAe,CAC7B,YAAmC,EACnC,cAAqD;IAErD,IAAI,YAAY,KAAK,SAAS,EAAE;QAC9B,OAAO,IAAI,8BAAa,EAAE,CAAC;KAC5B;IAED,QAAQ,YAAY,EAAE;QACpB,KAAK,OAAO;YACV,OAAO,IAAI,8BAAa,EAAE,CAAC;QAC7B,KAAK,SAAS;YACZ,IAAI,cAAc,KAAK,SAAS,EAAE;gBAChC,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,cAAc,EAAE;oBACxD,YAAY;iBACb,CAAC,CAAC;aACJ;YAED,IAAI,OAAO,cAAc,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC3C,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE;oBAC9D,YAAY;oBACZ,aAAa,EAAE,MAAM;iBACtB,CAAC,CAAC;aACJ;YAED,IAAI,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;gBACtD,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE;oBAC9D,YAAY;oBACZ,SAAS,EAAE,MAAM;oBACjB,MAAM,EAAE,qCAAqC;iBAC9C,CAAC,CAAC;aACJ;YAED,OAAO,IAAI,kCAAe,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D;YACE,MAAM,IAAI,sBAAa,CAAC,oBAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE;gBAC1D,YAAY;aACb,CAAC,CAAC;KACN;AACH,CAAC;AAvCD,0CAuCC;AAED,SAAS,sBAAsB,CAAC,SAAiB;IAC/C,MAAM,mBAAmB,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;QACpD,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,SAAS,CAAC;IAEd,OAAO,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,CAAC"}