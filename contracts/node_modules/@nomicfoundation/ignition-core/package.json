{"name": "@nomicfoundation/ignition-core", "version": "0.15.13", "license": "MIT", "author": "Nomic Foundation", "homepage": "https://hardhat.org", "repository": "github:NomicFoundation/hardhat-ignition", "description": "Hardhat Ignition is a declarative system for deploying smart contracts on Ethereum. It enables you to define smart contract instances you want to deploy, and any operation you want to run on them. By taking over the deployment and execution, Hardhat Ignition lets you focus on your project instead of getting caught up in the deployment details.", "keywords": ["ethereum", "smart-contracts", "hardhat", "blockchain", "dapps", "tooling", "solidity", "deployment"], "main": "./dist/src/index.js", "types": "./dist/src/index.d.ts", "exports": {".": "./dist/src/index.js", "./ui-helpers": "./dist/src/ui-helpers.js"}, "typesVersions": {"*": {"ui-helpers": ["./dist/src/ui-helpers.d.ts"]}}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "1.0.2", "@microsoft/api-extractor": "7.40.1", "@types/chai": "^4.2.22", "@types/chai-as-promised": "^7.1.5", "@types/debug": "^4.1.7", "@types/fs-extra": "^9.0.13", "@types/mocha": "9.1.1", "@types/node": "^20.0.0", "@types/ndjson": "2.0.1", "@types/lodash": "^4.14.123", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/experimental-utils": "^5.62.0", "@typescript-eslint/parser": "^5.57.1", "chai": "^4.3.4", "chai-as-promised": "7.1.1", "eslint": "^8.38.0", "eslint-config-prettier": "8.3.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "2.29.0", "eslint-plugin-mocha": "^9.0.0", "eslint-plugin-no-only-tests": "3.1.0", "eslint-plugin-prettier": "4.0.0", "hardhat": "^2.26.0", "mocha": "^9.1.3", "nyc": "15.1.0", "prettier": "2.8.8", "rimraf": "3.0.2", "ts-node": "10.9.1", "typescript": "^5.0.2", "@nomicfoundation/eslint-plugin-hardhat-internal-rules": "^1.0.2"}, "dependencies": {"@ethersproject/address": "5.6.1", "@nomicfoundation/solidity-analyzer": "^0.1.1", "cbor": "^9.0.0", "debug": "^4.3.2", "ethers": "^6.14.0", "fs-extra": "^10.0.0", "immer": "10.0.2", "lodash": "4.17.21", "ndjson": "2.0.0"}, "scripts": {"build": "tsc --build", "lint": "pnpm prettier --check && pnpm eslint && pnpm api-extractor", "lint:fix": "pnpm prettier --write && pnpm eslint --fix", "eslint": "eslint \"src/**/*.{ts,tsx}\" \"test/**/*.{ts,tsx}\" \"test-integrations/**/*.{ts,tsx}\"", "prettier": "prettier \"**/*.{js,ts,md,json}\"", "preapi-extractor": "pnpm build", "api-extractor": "api-extractor run --local --verbose", "test": "mocha --recursive \"test/**/*.ts\"", "test:debug": "DEBUG='ignition:*' pnpm test", "test:build": "tsc --project ./test/", "test:coverage": "nyc mocha --recursive \"test/**/*.ts\" \"test-integrations/**/*.ts\"", "test:integrations": "mocha --recursive \"test-integrations/**/*.ts\"", "pretest:ci": "cd ../.. && pnpm build", "test:ci": "pnpm test && pnpm pnpm test:integrations", "clean": "rimraf .nyc_output coverage dist tsconfig.tsbuildinfo"}}