{"name": "@nomicfoundation/ignition-ui", "version": "0.15.12", "type": "module", "devDependencies": {"@fontsource/roboto": "^5.0.8", "@types/chai": "^4.2.22", "@types/chai-as-promised": "^7.1.5", "@types/mocha": "9.1.1", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/styled-components": "5.1.26", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "chai": "^4.3.4", "chai-as-promised": "7.1.1", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "mermaid": "10.9.3", "mocha": "^9.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "6.11.0", "react-tooltip": "^5.21.4", "rimraf": "^3.0.2", "styled-components": "5.3.10", "svg-pan-zoom": "^3.6.1", "ts-node": "10.9.1", "typescript": "^5.0.2", "vite": "^5.4.17", "vite-plugin-singlefile": "^2.0.1", "@nomicfoundation/ignition-core": "^0.15.13"}, "scripts": {"predev": "pnpm regenerate-deployment-example", "dev": "vite --force", "build": "tsc --build . && vite build", "test": "mocha --loader=ts-node/esm --recursive \"test/**/*.ts\"", "test:coverage": "nyc mocha --loader=ts-node/esm --recursive \"test/**/*.ts\"", "regenerate-deployment-example": "node ./scripts/generate-example-deployment-json.js", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "clean": "rimraf dist tsconfig.tsbuildinfo"}}