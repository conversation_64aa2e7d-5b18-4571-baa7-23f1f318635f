export declare const TASK_VERIFY = "verify";
export declare const TASK_VERIFY_GET_VERIFICATION_SUBTASKS = "verify:get-verification-subtasks";
export declare const TASK_VERIFY_VERIFY = "verify:verify";
export declare const TASK_VERIFY_PRINT_SUPPORTED_NETWORKS = "verify:print-supported-networks";
export declare const TASK_VERIFY_GET_CONTRACT_INFORMATION = "verify:get-contract-information";
export declare const TASK_VERIFY_ETHERSCAN = "verify:etherscan";
export declare const TASK_VERIFY_ETHERSCAN_RESOLVE_ARGUMENTS = "verify:etherscan-resolve-arguments";
export declare const TASK_VERIFY_ETHERSCAN_GET_MINIMAL_INPUT = "verify:etherscan-get-minimal-input";
export declare const TASK_VERIFY_ETHERSCAN_ATTEMPT_VERIFICATION = "verify:etherscan-attempt-verification";
export declare const TASK_VERIFY_SOURCIFY = "verify:sourcify";
export declare const TASK_VERIFY_SOURCIFY_RESOLVE_ARGUMENTS = "verify:sourcify-resolve-arguments";
export declare const TASK_VERIFY_SOURCIFY_ATTEMPT_VERIFICATION = "verify:sourcify-attempt-verification";
export declare const TASK_VERIFY_SOURCIFY_DISABLED_WARNING = "verify:sourcify-disabled-warning";
export declare const TASK_VERIFY_BLOCKSCOUT = "verify:blockscout";
export declare const TASK_VERIFY_BLOCKSCOUT_RESOLVE_ARGUMENTS = "verify:blockscout-resolve-arguments";
export declare const TASK_VERIFY_BLOCKSCOUT_ATTEMPT_VERIFICATION = "verify:blockscout-attempt-verification";
//# sourceMappingURL=task-names.d.ts.map