{"name": "type-fest", "version": "0.7.1", "description": "A collection of essential TypeScript types", "license": "(MIT OR CC0-1.0)", "repository": "sindresorhus/type-fest", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && tsd"}, "files": ["index.d.ts", "source"], "keywords": ["typescript", "ts", "types", "utility", "util", "utilities", "omit", "merge", "json"], "devDependencies": {"@sindresorhus/tsconfig": "^0.4.0", "@typescript-eslint/eslint-plugin": "^1.9.0", "@typescript-eslint/parser": "^1.10.2", "eslint-config-xo-typescript": "^0.14.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"import/no-unresolved": "off", "@typescript-eslint/indent": "off"}}}